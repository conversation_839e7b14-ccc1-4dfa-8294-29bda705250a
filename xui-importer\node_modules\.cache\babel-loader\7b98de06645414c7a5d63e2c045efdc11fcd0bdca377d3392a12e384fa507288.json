{"ast": null, "code": "var _jsxFileName = \"F:\\\\WORKSPACE\\\\XUI IMPORTER\\\\xui-importer\\\\src\\\\components\\\\CategorySelectorTest.js\";\n/**\n * 🗂️ CategorySelector - Selector de Categorías y Bouquet<PERSON> (DEBUG VERSION)\n */\n\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategorySelector = ({\n  contentType = 'series',\n  selectedCategory,\n  onCategoryChange,\n  selectedBouquet,\n  onBouquetChange,\n  disabled = false\n}) => {\n  console.log('🎯 CategorySelector renderizado con:', {\n    contentType,\n    selectedCategory,\n    selectedBouquet,\n    disabled\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: '#f0f8ff',\n      border: '2px solid #007bff',\n      padding: '20px',\n      margin: '10px 0',\n      borderRadius: '8px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"\\uD83C\\uDFAF CategorySelector - FUNCIONANDO!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Content Type:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 10\n      }, this), \" \", contentType]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Selected Category:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 10\n      }, this), \" \", selectedCategory || 'None']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Selected Bouquet:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 10\n      }, this), \" \", selectedBouquet || 'None']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Disabled:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 10\n      }, this), \" \", disabled ? 'Yes' : 'No']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"\\uD83C\\uDFAD Test Bouquet Selector:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedBouquet || '',\n        onChange: e => onBouquetChange === null || onBouquetChange === void 0 ? void 0 : onBouquetChange(e.target.value),\n        style: {\n          width: '100%',\n          padding: '8px',\n          margin: '5px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"Select Bouquet...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"1\",\n          children: \"\\u2733\\uFE0FSeries\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"2\",\n          children: \"\\u2733\\uFE0FPeliculas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"3\",\n          children: \"\\u2733\\uFE0FTv en vivo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '15px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"\\uD83D\\uDDC2\\uFE0F Test Category Selector:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedCategory || '',\n        onChange: e => onCategoryChange === null || onCategoryChange === void 0 ? void 0 : onCategoryChange(e.target.value),\n        style: {\n          width: '100%',\n          padding: '8px',\n          margin: '5px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"Select Category...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"1\",\n          children: \"NOVELAS TURCAS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"2\",\n          children: \"SERIES ACCION\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"3\",\n          children: \"DRAMA SERIES\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '15px',\n        padding: '10px',\n        background: '#e8f5e8',\n        borderRadius: '4px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"\\u2705 Este componente est\\xE1 funcionando correctamente!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n        children: \"Ahora podemos implementar la funcionalidad completa con los endpoints del backend.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_c = CategorySelector;\nexport default CategorySelector;\nvar _c;\n$RefreshReg$(_c, \"CategorySelector\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "CategorySelector", "contentType", "selectedCate<PERSON><PERSON>", "onCategoryChange", "selectedBouquet", "onBouquetChange", "disabled", "console", "log", "style", "background", "border", "padding", "margin", "borderRadius", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "value", "onChange", "e", "target", "width", "_c", "$RefreshReg$"], "sources": ["F:/WORKSPACE/XUI IMPORTER/xui-importer/src/components/CategorySelectorTest.js"], "sourcesContent": ["/**\n * 🗂️ CategorySelector - Selector de Categorías y Bouquets (DEBUG VERSION)\n */\n\nimport React from 'react';\n\nconst CategorySelector = ({ \n  contentType = 'series', \n  selectedCategory, \n  onCategoryChange,\n  selectedBouquet,\n  onBouquetChange,\n  disabled = false \n}) => {\n  console.log('🎯 CategorySelector renderizado con:', { contentType, selectedCategory, selectedBouquet, disabled });\n  \n  return (\n    <div style={{ \n      background: '#f0f8ff', \n      border: '2px solid #007bff', \n      padding: '20px', \n      margin: '10px 0',\n      borderRadius: '8px'\n    }}>\n      <h3>🎯 CategorySelector - FUNCIONANDO!</h3>\n      <p><strong>Content Type:</strong> {contentType}</p>\n      <p><strong>Selected Category:</strong> {selectedCategory || 'None'}</p>\n      <p><strong>Selected Bouquet:</strong> {selectedBouquet || 'None'}</p>\n      <p><strong>Disabled:</strong> {disabled ? 'Yes' : 'No'}</p>\n      \n      <div style={{ marginTop: '15px' }}>\n        <label>🎭 Test Bouquet Selector:</label>\n        <select \n          value={selectedBouquet || ''} \n          onChange={(e) => onBouquetChange?.(e.target.value)}\n          style={{ width: '100%', padding: '8px', margin: '5px 0' }}\n        >\n          <option value=\"\">Select Bouquet...</option>\n          <option value=\"1\">✳️Series</option>\n          <option value=\"2\">✳️Peliculas</option>\n          <option value=\"3\">✳️Tv en vivo</option>\n        </select>\n      </div>\n      \n      <div style={{ marginTop: '15px' }}>\n        <label>🗂️ Test Category Selector:</label>\n        <select \n          value={selectedCategory || ''} \n          onChange={(e) => onCategoryChange?.(e.target.value)}\n          style={{ width: '100%', padding: '8px', margin: '5px 0' }}\n        >\n          <option value=\"\">Select Category...</option>\n          <option value=\"1\">NOVELAS TURCAS</option>\n          <option value=\"2\">SERIES ACCION</option>\n          <option value=\"3\">DRAMA SERIES</option>\n        </select>\n      </div>\n      \n      <div style={{ marginTop: '15px', padding: '10px', background: '#e8f5e8', borderRadius: '4px' }}>\n        <strong>✅ Este componente está funcionando correctamente!</strong>\n        <br />\n        <small>Ahora podemos implementar la funcionalidad completa con los endpoints del backend.</small>\n      </div>\n    </div>\n  );\n};\n\nexport default CategorySelector;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,gBAAgB,GAAGA,CAAC;EACxBC,WAAW,GAAG,QAAQ;EACtBC,gBAAgB;EAChBC,gBAAgB;EAChBC,eAAe;EACfC,eAAe;EACfC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;IAAEP,WAAW;IAAEC,gBAAgB;IAAEE,eAAe;IAAEE;EAAS,CAAC,CAAC;EAEjH,oBACEP,OAAA;IAAKU,KAAK,EAAE;MACVC,UAAU,EAAE,SAAS;MACrBC,MAAM,EAAE,mBAAmB;MAC3BC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,QAAQ;MAChBC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBACAhB,OAAA;MAAAgB,QAAA,EAAI;IAAkC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3CpB,OAAA;MAAAgB,QAAA,gBAAGhB,OAAA;QAAAgB,QAAA,EAAQ;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAAClB,WAAW;IAAA;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnDpB,OAAA;MAAAgB,QAAA,gBAAGhB,OAAA;QAAAgB,QAAA,EAAQ;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACjB,gBAAgB,IAAI,MAAM;IAAA;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvEpB,OAAA;MAAAgB,QAAA,gBAAGhB,OAAA;QAAAgB,QAAA,EAAQ;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACf,eAAe,IAAI,MAAM;IAAA;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrEpB,OAAA;MAAAgB,QAAA,gBAAGhB,OAAA;QAAAgB,QAAA,EAAQ;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACb,QAAQ,GAAG,KAAK,GAAG,IAAI;IAAA;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE3DpB,OAAA;MAAKU,KAAK,EAAE;QAAEW,SAAS,EAAE;MAAO,CAAE;MAAAL,QAAA,gBAChChB,OAAA;QAAAgB,QAAA,EAAO;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxCpB,OAAA;QACEsB,KAAK,EAAEjB,eAAe,IAAI,EAAG;QAC7BkB,QAAQ,EAAGC,CAAC,IAAKlB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGkB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QACnDZ,KAAK,EAAE;UAAEgB,KAAK,EAAE,MAAM;UAAEb,OAAO,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAE,QAAA,gBAE1DhB,OAAA;UAAQsB,KAAK,EAAC,EAAE;UAAAN,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC3CpB,OAAA;UAAQsB,KAAK,EAAC,GAAG;UAAAN,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnCpB,OAAA;UAAQsB,KAAK,EAAC,GAAG;UAAAN,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtCpB,OAAA;UAAQsB,KAAK,EAAC,GAAG;UAAAN,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENpB,OAAA;MAAKU,KAAK,EAAE;QAAEW,SAAS,EAAE;MAAO,CAAE;MAAAL,QAAA,gBAChChB,OAAA;QAAAgB,QAAA,EAAO;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1CpB,OAAA;QACEsB,KAAK,EAAEnB,gBAAgB,IAAI,EAAG;QAC9BoB,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAGoB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QACpDZ,KAAK,EAAE;UAAEgB,KAAK,EAAE,MAAM;UAAEb,OAAO,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAE,QAAA,gBAE1DhB,OAAA;UAAQsB,KAAK,EAAC,EAAE;UAAAN,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5CpB,OAAA;UAAQsB,KAAK,EAAC,GAAG;UAAAN,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACzCpB,OAAA;UAAQsB,KAAK,EAAC,GAAG;UAAAN,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxCpB,OAAA;UAAQsB,KAAK,EAAC,GAAG;UAAAN,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENpB,OAAA;MAAKU,KAAK,EAAE;QAAEW,SAAS,EAAE,MAAM;QAAER,OAAO,EAAE,MAAM;QAAEF,UAAU,EAAE,SAAS;QAAEI,YAAY,EAAE;MAAM,CAAE;MAAAC,QAAA,gBAC7FhB,OAAA;QAAAgB,QAAA,EAAQ;MAAiD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAClEpB,OAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNpB,OAAA;QAAAgB,QAAA,EAAO;MAAkF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACO,EAAA,GA3DI1B,gBAAgB;AA6DtB,eAAeA,gBAAgB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}