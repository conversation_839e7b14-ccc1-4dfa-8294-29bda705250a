/**
 * 🎬 API de TMDB para obtener metadata
 * Integración con The Movie Database para series y películas
 */

const axios = require('axios');
require('dotenv').config();

class TMDBService {
  constructor() {
    this.apiKey = process.env.TMDB_API_KEY;
    this.baseURL = process.env.TMDB_BASE_URL || 'https://api.themoviedb.org/3';
    this.imageBaseURL = 'https://image.tmdb.org/t/p';
    
    if (!this.apiKey) {
      console.warn('⚠️ TMDB_API_KEY no configurada. Las funciones de metadata estarán limitadas.');
    }
  }

  /**
   * 🔍 Buscar serie por nombre
   */
  async searchSeries(seriesName, year = null) {
    try {
      if (!this.apiKey) {
        throw new Error('TMDB API key no configurada');
      }

      const params = {
        api_key: this.apiKey,
        query: seriesName,
        language: 'es-ES'
      };

      if (year) {
        params.first_air_date_year = year;
      }

      const response = await axios.get(`${this.baseURL}/search/tv`, { params });
      
      return {
        success: true,
        results: response.data.results.map(this.formatSeriesData.bind(this)),
        total_results: response.data.total_results
      };
      
    } catch (error) {
      console.error('❌ Error buscando serie en TMDB:', error.message);
      return {
        success: false,
        message: error.message,
        results: []
      };
    }
  }

  /**
   * 📺 Obtener detalles completos de serie
   */
  async getSeriesDetails(tmdbId) {
    try {
      if (!this.apiKey) {
        throw new Error('TMDB API key no configurada');
      }

      const response = await axios.get(`${this.baseURL}/tv/${tmdbId}`, {
        params: {
          api_key: this.apiKey,
          language: 'es-ES',
          append_to_response: 'credits,videos,similar'
        }
      });

      return {
        success: true,
        data: this.formatSeriesData(response.data, true)
      };
      
    } catch (error) {
      console.error('❌ Error obteniendo detalles de serie:', error.message);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * 🎬 Buscar película por nombre
   */
  async searchMovie(movieName, year = null) {
    try {
      if (!this.apiKey) {
        throw new Error('TMDB API key no configurada');
      }

      const params = {
        api_key: this.apiKey,
        query: movieName,
        language: 'es-ES'
      };

      if (year) {
        params.year = year;
      }

      const response = await axios.get(`${this.baseURL}/search/movie`, { params });
      
      return {
        success: true,
        results: response.data.results.map(this.formatMovieData.bind(this)),
        total_results: response.data.total_results
      };
      
    } catch (error) {
      console.error('❌ Error buscando película en TMDB:', error.message);
      return {
        success: false,
        message: error.message,
        results: []
      };
    }
  }

  /**
   * 🎬 Obtener detalles completos de película
   */
  async getMovieDetails(movieId) {
    try {
      if (!this.apiKey) {
        throw new Error('TMDB API key no configurada');
      }

      const params = {
        api_key: this.apiKey,
        language: 'es-ES'
      };

      const response = await axios.get(`${this.baseURL}/movie/${movieId}`, { params });
      return response.data;

    } catch (error) {
      console.error('❌ Error obteniendo detalles de película:', error.message);
      return null;
    }
  }

  /**
   * 🎬 Obtener créditos de película (cast y crew)
   */
  async getMovieCredits(movieId) {
    try {
      if (!this.apiKey) {
        throw new Error('TMDB API key no configurada');
      }

      const params = {
        api_key: this.apiKey,
        language: 'es-ES'
      };

      const response = await axios.get(`${this.baseURL}/movie/${movieId}/credits`, { params });
      return response.data;

    } catch (error) {
      console.error('❌ Error obteniendo créditos de película:', error.message);
      return null;
    }
  }

  /**
   * 🎬 Obtener videos de película (trailers)
   */
  async getMovieVideos(movieId) {
    try {
      if (!this.apiKey) {
        throw new Error('TMDB API key no configurada');
      }

      const params = {
        api_key: this.apiKey,
        language: 'es-ES'
      };

      const response = await axios.get(`${this.baseURL}/movie/${movieId}/videos`, { params });
      return response.data;

    } catch (error) {
      console.error('❌ Error obteniendo videos de película:', error.message);
      return null;
    }
  }

  /**
   * 🎯 Formatear datos de serie para XUI
   */
  formatSeriesData(tmdbSeries, includeExtras = false) {
    const formatted = {
      tmdb_id: tmdbSeries.id,
      title: tmdbSeries.name || tmdbSeries.original_name,
      plot: tmdbSeries.overview || '',
      rating: tmdbSeries.vote_average || 0,
      year: tmdbSeries.first_air_date ? new Date(tmdbSeries.first_air_date).getFullYear() : null,
      release_date: tmdbSeries.first_air_date || null,
      cover: tmdbSeries.poster_path ? `${this.imageBaseURL}/w500${tmdbSeries.poster_path}` : '',
      cover_big: tmdbSeries.poster_path ? `${this.imageBaseURL}/original${tmdbSeries.poster_path}` : '',
      backdrop_path: tmdbSeries.backdrop_path ? `${this.imageBaseURL}/original${tmdbSeries.backdrop_path}` : '',
      genre: tmdbSeries.genres ? tmdbSeries.genres.map(g => g.name).join(', ') : '',
      episode_run_time: tmdbSeries.episode_run_time?.[0] || 0
    };

    if (includeExtras && tmdbSeries.credits) {
      formatted.cast = tmdbSeries.credits.cast?.slice(0, 10).map(c => c.name).join(', ') || '';
      formatted.director = tmdbSeries.credits.crew?.find(c => c.job === 'Director')?.name || '';
    }

    if (includeExtras && tmdbSeries.videos) {
      const trailer = tmdbSeries.videos.results?.find(v => v.type === 'Trailer' && v.site === 'YouTube');
      formatted.youtube_trailer = trailer ? `https://www.youtube.com/watch?v=${trailer.key}` : '';
    }

    if (includeExtras && tmdbSeries.similar) {
      formatted.similar = tmdbSeries.similar.results?.slice(0, 5).map(s => ({
        id: s.id,
        name: s.name,
        poster_path: s.poster_path
      })) || [];
    }

    if (tmdbSeries.seasons) {
      formatted.seasons = tmdbSeries.seasons.map(season => ({
        season_number: season.season_number,
        episode_count: season.episode_count,
        name: season.name,
        poster_path: season.poster_path ? `${this.imageBaseURL}/w500${season.poster_path}` : '',
        air_date: season.air_date,
        overview: season.overview
      }));
    }

    return formatted;
  }

  /**
   * 🎬 Formatear datos de película para XUI
   */
  formatMovieData(tmdbMovie, includeExtras = false) {
    const formatted = {
      tmdb_id: tmdbMovie.id,
      title: tmdbMovie.title || tmdbMovie.original_title,
      plot: tmdbMovie.overview || '',
      rating: tmdbMovie.vote_average || 0,
      year: tmdbMovie.release_date ? new Date(tmdbMovie.release_date).getFullYear() : null,
      release_date: tmdbMovie.release_date || null,
      cover: tmdbMovie.poster_path ? `${this.imageBaseURL}/w500${tmdbMovie.poster_path}` : '',
      cover_big: tmdbMovie.poster_path ? `${this.imageBaseURL}/original${tmdbMovie.poster_path}` : '',
      backdrop_path: tmdbMovie.backdrop_path ? `${this.imageBaseURL}/original${tmdbMovie.backdrop_path}` : '',
      genre: tmdbMovie.genres ? tmdbMovie.genres.map(g => g.name).join(', ') : '',
      runtime: tmdbMovie.runtime || 0
    };

    if (includeExtras && tmdbMovie.credits) {
      formatted.cast = tmdbMovie.credits.cast?.slice(0, 10).map(c => c.name).join(', ') || '';
      formatted.director = tmdbMovie.credits.crew?.find(c => c.job === 'Director')?.name || '';
    }

    return formatted;
  }

  /**
   * 🧪 Probar conexión con TMDB
   */
  async testConnection() {
    try {
      if (!this.apiKey) {
        return {
          success: false,
          message: 'TMDB API key no configurada'
        };
      }

      const response = await axios.get(`${this.baseURL}/configuration`, {
        params: { api_key: this.apiKey }
      });

      return {
        success: true,
        message: 'Conexión TMDB exitosa',
        data: {
          base_url: response.data.images.secure_base_url,
          poster_sizes: response.data.images.poster_sizes
        }
      };
      
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.status_message || error.message
      };
    }
  }
}

module.exports = new TMDBService();
