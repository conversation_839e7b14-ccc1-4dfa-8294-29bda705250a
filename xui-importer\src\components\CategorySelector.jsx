/**
 * 🗂️ CategorySelector - Selector de Categorías y Bouquets
 * Funciona para Series, Películas y Canales de TV
 */

import React, { useState, useEffect } from 'react';
import { databaseAPI } from '../services/apiService';

const CategorySelector = ({ 
  contentType = 'series', 
  selectedCategory, 
  onCategoryChange,
  selectedBouquet,
  onBouquetChange,
  disabled = false 
}) => {
  const [categories, setCategories] = useState([]);
  const [bouquets, setBouquets] = useState([]);
  const [bouquetCategories, setBouquetCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Mapeo de tipos de contenido (coincide con ImportM3U)
  const contentTypeMapping = {
    'series': { type: 'series', label: 'Series', icon: '📺', dbType: '3' },
    'movie': { type: 'movies', label: 'Películas', icon: '🎬', dbType: '2' },
    'live': { type: 'live', label: 'TV en Vivo', icon: '📡', dbType: '1' },
    'radio': { type: 'live', label: 'Radio', icon: '📻', dbType: '1' }
  };

  const currentType = contentTypeMapping[contentType] || contentTypeMapping['series'];

  console.log('🎯 CategorySelector:', { contentType, currentType, selectedCategory, selectedBouquet });

  // Cargar bouquets al montar
  useEffect(() => {
    loadBouquets();
  }, []);

  // Cargar categorías cuando cambia el tipo de contenido o bouquet
  useEffect(() => {
    if (selectedBouquet) {
      loadBouquetCategories(selectedBouquet);
    } else {
      loadCategoriesByType();
    }
  }, [contentType, selectedBouquet]);

  const loadBouquets = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Cargando bouquets...');
      
      const response = await databaseAPI.getBouquets();
      console.log('📦 Bouquets cargados:', response);
      
      setBouquets(response.data || []);
    } catch (err) {
      console.error('❌ Error cargando bouquets:', err);
      setError('Error cargando bouquets');
    } finally {
      setLoading(false);
    }
  };

  const loadCategoriesByType = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log(`🔄 Cargando categorías tipo: ${currentType.type}`);
      
      const response = await databaseAPI.getCategoriesByType(currentType.type);
      console.log('📂 Categorías cargadas:', response);
      
      setCategories(response.data || []);
      setBouquetCategories([]);
    } catch (err) {
      console.error('❌ Error cargando categorías:', err);
      setError('Error cargando categorías');
    } finally {
      setLoading(false);
    }
  };

  const loadBouquetCategories = async (bouquetId) => {
    try {
      setLoading(true);
      setError(null);
      console.log(`🔄 Cargando categorías del bouquet: ${bouquetId} para tipo: ${currentType.type}`);
      console.log(`🔗 URL: /api/database/bouquet/${bouquetId}/categories?content_type=${currentType.type}`);

      const response = await databaseAPI.getBouquetCategories(bouquetId, currentType.type);
      console.log('📦 Categorías del bouquet cargadas:', response);
      console.log('📊 Número de categorías:', response.data ? response.data.length : 0);

      if (response.data && response.data.length > 0) {
        console.log('📋 Primera categoría:', response.data[0]);
      }

      setBouquetCategories(response.data || []);
      setCategories([]);
    } catch (err) {
      console.error('❌ Error cargando categorías del bouquet:', err);
      console.error('❌ Error completo:', err);
      setError(`Error cargando categorías del bouquet: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleBouquetChange = (e) => {
    const bouquetId = e.target.value;
    console.log('🎭 Bouquet seleccionado:', bouquetId);
    onBouquetChange?.(bouquetId);
    
    // Limpiar categoría seleccionada al cambiar bouquet
    onCategoryChange?.(null);
  };

  const handleCategoryChange = (e) => {
    const categoryId = e.target.value;
    console.log('🗂️ Categoría seleccionada:', categoryId);
    onCategoryChange?.(categoryId);
  };

  const availableCategories = selectedBouquet ? bouquetCategories : categories;

  return (
    <div className="category-selector">
      <div className="category-selector-header">
        <h3>
          {currentType.icon} Selección Manual de Categorías para {currentType.label}
        </h3>
        <p className="selection-info">
          📋 Selecciona manualmente el bouquet y la categoría donde se organizará el contenido importado
        </p>
        {error && (
          <div className="error-message">
            ⚠️ {error}
          </div>
        )}
      </div>

      {/* Selector de Bouquet */}
      <div className="form-group">
        <label htmlFor="bouquet-select">
          🎭 1. Seleccionar Bouquet (Opcional):
        </label>
        <select
          id="bouquet-select"
          value={selectedBouquet || ''}
          onChange={handleBouquetChange}
          disabled={disabled || loading}
          className="form-control"
        >
          <option value="">📋 Mostrar todas las categorías disponibles</option>
          {bouquets.map(bouquet => (
            <option key={bouquet.id} value={bouquet.id}>
              {bouquet.bouquet_name} ({bouquet.total_categories} categorías)
            </option>
          ))}
        </select>
        <small className="form-text">
          💡 Los bouquets filtran las categorías por paquete/cliente. Si no seleccionas ninguno, verás todas las categorías disponibles.
        </small>
      </div>

      {/* Selector de Categoría */}
      <div className="form-group">
        <label htmlFor="category-select">
          🗂️ 2. Seleccionar Categoría de {currentType.label} (Requerido):
        </label>
        <select
          id="category-select"
          value={selectedCategory || ''}
          onChange={handleCategoryChange}
          disabled={disabled || loading}
          className="form-control"
          required
        >
          <option value="">
            {loading ? '⏳ Cargando categorías...' : `📂 Elige dónde organizar las ${currentType.label.toLowerCase()}`}
          </option>
          {availableCategories.map(category => (
            <option key={category.id} value={category.id}>
              {category.category_name} ({category.streams_count} streams)
              {category.is_adult === 1 && ' 🔞'}
            </option>
          ))}
        </select>
        <small className="form-text">
          {selectedBouquet 
            ? `📦 Mostrando ${availableCategories.length} categorías del bouquet "${bouquets.find(b => b.id == selectedBouquet)?.bouquet_name}"`
            : `📋 Mostrando todas las ${availableCategories.length} categorías de ${currentType.label.toLowerCase()} disponibles`
          }
        </small>
      </div>

      {/* Debug Info */}
      {selectedBouquet && (
        <div style={{ background: '#fff3cd', padding: '10px', margin: '10px 0', borderRadius: '4px', fontSize: '0.9rem' }}>
          <strong>🔍 Debug Info:</strong><br />
          Bouquet ID: {selectedBouquet}<br />
          Content Type: {currentType.type}<br />
          Available Categories: {availableCategories.length}<br />
          Loading: {loading ? 'Yes' : 'No'}<br />
          {error && <span style={{color: 'red'}}>Error: {error}</span>}
        </div>
      )}

      {/* Información adicional */}
      {selectedCategory && (
        <div className="category-info">
          {(() => {
            const category = availableCategories.find(c => c.id == selectedCategory);
            return category ? (
              <div className="info-card">
                <h4>📋 Categoría Seleccionada:</h4>
                <p><strong>Nombre:</strong> {category.category_name}</p>
                <p><strong>Tipo:</strong> {currentType.label}</p>
                <p><strong>Streams:</strong> {category.streams_count}</p>
                {category.is_adult === 1 && (
                  <p><strong>⚠️ Contenido:</strong> <span className="adult-content">Adulto 🔞</span></p>
                )}
              </div>
            ) : null;
          })()}
        </div>
      )}
    </div>
  );
};

export default CategorySelector;
