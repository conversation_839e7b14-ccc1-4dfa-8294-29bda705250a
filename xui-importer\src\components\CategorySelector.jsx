/**
 * 🗂️ CategorySelector - Selector de Categorías y Bouquets
 * Permite seleccionar categorías por tipo de contenido y bouquet
 */

import React, { useState, useEffect } from 'react';
import { databaseAPI } from '../services/apiService';

const CategorySelector = ({ 
  contentType = 'series', 
  selectedCategory, 
  onCategoryChange,
  selectedBouquet,
  onBouquetChange,
  disabled = false 
}) => {
  const [categories, setCategories] = useState([]);
  const [bouquets, setBouquets] = useState([]);
  const [bouquetCategories, setBouquetCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Mapeo de tipos de contenido
  const contentTypeMapping = {
    'series': { type: 'series', label: 'Series', icon: '📺' },
    'movies': { type: 'movies', label: 'Películas', icon: '🎬' },
    'live': { type: 'live', label: 'TV en Vivo', icon: '📡' }
  };

  const currentType = contentTypeMapping[contentType] || contentTypeMapping['series'];

  // Cargar bouquets al montar
  useEffect(() => {
    loadBouquets();
  }, []);

  // Cargar categorías cuando cambia el tipo de contenido o bouquet
  useEffect(() => {
    if (selectedBouquet) {
      loadBouquetCategories(selectedBouquet);
    } else {
      loadCategoriesByType();
    }
  }, [contentType, selectedBouquet]);

  const loadBouquets = async () => {
    try {
      setLoading(true);
      const response = await databaseAPI.getBouquets();
      setBouquets(response.data || []);
    } catch (err) {
      console.error('Error cargando bouquets:', err);
      setError('Error cargando bouquets');
    } finally {
      setLoading(false);
    }
  };

  const loadCategoriesByType = async () => {
    try {
      setLoading(true);
      const response = await databaseAPI.getCategoriesByType(currentType.type);
      setCategories(response.data || []);
      setBouquetCategories([]);
    } catch (err) {
      console.error('Error cargando categorías:', err);
      setError('Error cargando categorías');
    } finally {
      setLoading(false);
    }
  };

  const loadBouquetCategories = async (bouquetId) => {
    try {
      setLoading(true);
      const response = await databaseAPI.getBouquetCategories(bouquetId, currentType.type);
      setBouquetCategories(response.data || []);
      setCategories([]);
    } catch (err) {
      console.error('Error cargando categorías del bouquet:', err);
      setError('Error cargando categorías del bouquet');
    } finally {
      setLoading(false);
    }
  };

  const handleBouquetChange = (e) => {
    const bouquetId = e.target.value;
    onBouquetChange?.(bouquetId);
    
    // Limpiar categoría seleccionada al cambiar bouquet
    onCategoryChange?.(null);
  };

  const handleCategoryChange = (e) => {
    const categoryId = e.target.value;
    onCategoryChange?.(categoryId);
  };

  const availableCategories = selectedBouquet ? bouquetCategories : categories;

  return (
    <div className="category-selector">
      <div className="category-selector-header">
        <h3>
          {currentType.icon} Categorías para {currentType.label}
        </h3>
        {error && (
          <div className="error-message">
            ⚠️ {error}
          </div>
        )}
      </div>

      {/* Selector de Bouquet */}
      <div className="form-group">
        <label htmlFor="bouquet-select">
          🎭 Bouquet (Opcional):
        </label>
        <select
          id="bouquet-select"
          value={selectedBouquet || ''}
          onChange={handleBouquetChange}
          disabled={disabled || loading}
          className="form-control"
        >
          <option value="">📋 Todas las categorías</option>
          {bouquets.map(bouquet => (
            <option key={bouquet.id} value={bouquet.id}>
              {bouquet.bouquet_name} ({bouquet.total_categories} categorías)
            </option>
          ))}
        </select>
        <small className="form-text">
          Los bouquets agrupan categorías por cliente/paquete
        </small>
      </div>

      {/* Selector de Categoría */}
      <div className="form-group">
        <label htmlFor="category-select">
          🗂️ Categoría de {currentType.label}:
        </label>
        <select
          id="category-select"
          value={selectedCategory || ''}
          onChange={handleCategoryChange}
          disabled={disabled || loading}
          className="form-control"
          required
        >
          <option value="">
            {loading ? '⏳ Cargando...' : `📂 Seleccionar categoría de ${currentType.label.toLowerCase()}`}
          </option>
          {availableCategories.map(category => (
            <option key={category.id} value={category.id}>
              {category.category_name} ({category.streams_count} streams)
              {category.is_adult === 1 && ' 🔞'}
            </option>
          ))}
        </select>
        <small className="form-text">
          {selectedBouquet 
            ? `Categorías del bouquet seleccionado (${availableCategories.length})`
            : `Todas las categorías de ${currentType.label.toLowerCase()} (${availableCategories.length})`
          }
        </small>
      </div>

      {/* Información adicional */}
      {selectedCategory && (
        <div className="category-info">
          {(() => {
            const category = availableCategories.find(c => c.id == selectedCategory);
            return category ? (
              <div className="info-card">
                <h4>📋 Categoría Seleccionada:</h4>
                <p><strong>Nombre:</strong> {category.category_name}</p>
                <p><strong>Tipo:</strong> {currentType.label}</p>
                <p><strong>Streams:</strong> {category.streams_count}</p>
                {category.is_adult === 1 && (
                  <p><strong>⚠️ Contenido:</strong> <span className="adult-content">Adulto 🔞</span></p>
                )}
              </div>
            ) : null;
          })()}
        </div>
      )}
    </div>
  );
};

export default CategorySelector;
