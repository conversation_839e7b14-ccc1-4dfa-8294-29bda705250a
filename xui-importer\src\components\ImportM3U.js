import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Button, Form, Alert, ProgressBar, Table, Badge } from 'react-bootstrap';
import SeriesImportHandler from './SeriesImportHandler';
import BackendStatus from './BackendStatus';

import {
  checkSystemHealth
} from '../utils/seriesLogic';
import { api, databaseAPI } from '../services/apiService';

const ImportM3U = () => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState('info');
  
  // Estados para backend
  const [backendStatus, setBackendStatus] = useState('checking');
  const [fileAnalysis, setFileAnalysis] = useState(null);
  const [isProcessingFile, setIsProcessingFile] = useState(false);
  
  // Nuevos estados para configuración XUI
  const [contentType, setContentType] = useState('');
  const [streamsServer, setStreamsServer] = useState('');
  const [sourceConfig, setSourceConfig] = useState({
    directSource: true,
    directProxy: false,
    loadBalancing: false
  });
  const [selectedCategories, setSelectedCategories] = useState([]);



  // Estados para datos dinámicos del backend
  const [availableServers, setAvailableServers] = useState([]);
  const [existingCategories, setExistingCategories] = useState([]);

  const checkBackendStatus = async () => {
    try {
      const health = await checkSystemHealth();
      setBackendStatus(health.success ? 'connected' : 'error');
      
      if (!health.success) {
        displayAlert('warning', 'Backend no disponible. Funcionando en modo offline.');
      }
    } catch (error) {
      setBackendStatus('error');
      displayAlert('danger', 'No se puede conectar al backend');
    }
  };

  const loadInitialData = async () => {
    try {
      console.log(`🔄 Cargando datos iniciales. Backend status: ${backendStatus}`);

      // Cargar servidores y categorías desde backend si está disponible
      if (backendStatus === 'connected') {
        console.log('✅ Backend conectado, cargando datos reales...');
        await loadRealServers();
        await loadRealCategories();
      } else {
        console.log('⚠️ Backend no conectado, usando datos mock...');
        // Fallback a mock data si no hay conexión
        loadMockData();
      }

    } catch (error) {
      console.error('Error cargando datos iniciales:', error);
      if (window.debugLog) {
        window.debugLog('error', `Error cargando datos iniciales: ${error.message}`);
      }
      // Fallback a mock data en caso de error
      loadMockData();
    }
  };

  // Cargar servidores reales desde la base de datos
  const loadRealServers = async () => {
    try {
      console.log('🔄 Iniciando carga de servidores reales...');

      const response = await fetch('http://localhost:5001/api/database/streaming-servers');
      console.log('📡 Respuesta del servidor:', response.status, response.statusText);

      const result = await response.json();
      console.log('📊 Datos recibidos:', result);

      if (result.success && result.data) {
        const servers = result.data.map(server => ({
          id: server.server_id,
          name: server.server_name || `Server ${server.server_id}`,
          ip: server.server_ip || 'Unknown IP',
          load: `${server.total_streams || 0} streams`, // Mostrar cantidad de streams como "carga"
          total_streams: server.total_streams || 0,
          status: server.server_status === 1 ? 'Active' : 'Inactive'
        }));

        console.log('🖥️ Servidores mapeados:', servers);
        setAvailableServers(servers);
        console.log('✅ Estado actualizado con', servers.length, 'servidores');

        if (window.debugLog) {
          window.debugLog('success', `✅ Cargados ${servers.length} servidores reales desde BD`);
        }
      } else {
        console.error('❌ Respuesta no exitosa:', result);
        throw new Error(result.error || 'No se pudieron cargar servidores');
      }
    } catch (error) {
      console.error('❌ Error cargando servidores:', error);
      if (window.debugLog) {
        window.debugLog('error', `❌ Error cargando servidores: ${error.message}`);
      }
      throw error;
    }
  };

  // Cargar categorías reales desde la base de datos
  const loadRealCategories = async () => {
    try {
      const result = await databaseAPI.getCategories();

      if (result.success && result.data) {
        const categories = result.data.map(cat => ({
          id: cat.category_id,
          name: cat.category_name,
          type: detectCategoryType(cat.category_name), // Detectar tipo basado en nombre
          parent_id: cat.parent_id
        }));

        setExistingCategories(categories);

        if (window.debugLog) {
          window.debugLog('success', `✅ Cargadas ${categories.length} categorías reales desde BD`);
        }
      } else {
        throw new Error('No se pudieron cargar categorías');
      }
    } catch (error) {
      console.error('Error cargando categorías:', error);
      if (window.debugLog) {
        window.debugLog('error', `❌ Error cargando categorías: ${error.message}`);
      }
      throw error;
    }
  };

  // Detectar tipo de categoría basado en el nombre
  const detectCategoryType = (categoryName) => {
    const name = categoryName.toLowerCase();

    if (name.includes('movie') || name.includes('film') || name.includes('cinema')) {
      return 'movie';
    } else if (name.includes('series') || name.includes('show') || name.includes('drama')) {
      return 'series';
    } else if (name.includes('live') || name.includes('tv') || name.includes('channel') || name.includes('news') || name.includes('sport')) {
      return 'live';
    } else if (name.includes('radio') || name.includes('music') || name.includes('fm')) {
      return 'radio';
    }

    return 'live'; // Default a live si no se puede detectar
  };

  // Datos mock como fallback
  const loadMockData = () => {
    setAvailableServers([
      { id: 1, name: 'Main Server US', ip: '*************', load: '45%' },
      { id: 2, name: 'EU Server', ip: '*************', load: '32%' },
      { id: 3, name: 'Asia Server', ip: '*************', load: '67%' },
      { id: 4, name: 'Backup Server', ip: '*************', load: '12%' }
    ]);

    setExistingCategories([
      { id: 1, name: 'Action Movies', type: 'movie' },
      { id: 2, name: 'Comedy Movies', type: 'movie' },
      { id: 3, name: 'Drama Series', type: 'series' },
      { id: 4, name: 'Comedy Series', type: 'series' },
      { id: 5, name: 'News Channels', type: 'live' },
      { id: 6, name: 'Sports Channels', type: 'live' },
      { id: 7, name: 'Entertainment', type: 'live' },
      { id: 8, name: 'Music Radio', type: 'radio' },
      { id: 9, name: 'Talk Radio', type: 'radio' }
    ]);

    if (window.debugLog) {
      window.debugLog('warning', '⚠️ Usando datos mock - backend no disponible');
    }
  };

  const showAlertMessage = (message, type = 'info') => {
    setAlertMessage(message);
    setAlertType(type);
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 5000);
  };

  // Helper function to show alert with better formatting
  const displayAlert = (type, message) => {
    showAlertMessage(message, type);
  };

  // File handling functions
  const handleBrowseFiles = () => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.m3u,.m3u8';
    fileInput.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        handleFileSelect({ target: { files: [file] } });
      }
    };
    fileInput.click();
  };

  const handleAnalyzeFile = async () => {
    if (!selectedFile) return;
    
    setIsProcessingFile(true);
    setFileAnalysis(null);
    
    try {
      displayAlert('info', 'Analizando archivo M3U...');
      
      const response = await api.m3uAPI.analyzeFile(selectedFile);
      
      if (response.success) {
        setFileAnalysis(response.data);

        // Auto-detectar content type basado en el análisis
        const detectedType = detectContentType(response.data);
        if (detectedType) {
          setContentType(detectedType);
          displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${response.data.basic_analysis?.estimated_entries || 0} entradas. Tipo detectado: ${getContentTypeLabel(detectedType)}`);
        } else {
          displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${response.data.basic_analysis?.estimated_entries || 0} entradas.`);
        }
      } else {
        throw new Error(response.error || 'Error analizando archivo');
      }
    } catch (error) {
      console.error('Error analyzing file:', error);
      displayAlert('danger', `❌ Error analizando archivo: ${error.message}`);
    } finally {
      setIsProcessingFile(false);
    }
  };

  const handleClearAnalysis = () => {
    setFileAnalysis(null);
    setSelectedFile(null);
    setContentType(''); // Limpiar también el content type
    displayAlert('info', 'Análisis limpiado. Selecciona un nuevo archivo.');
  };

  // Función para detectar automáticamente el tipo de contenido
  const detectContentType = (analysis) => {
    if (!analysis || !analysis.content_breakdown) return null;

    const breakdown = analysis.content_breakdown;
    const total = breakdown.live_tv + breakdown.movies + breakdown.series + breakdown.radio + breakdown.unknown;

    if (total === 0) return null;

    // Calcular porcentajes
    const percentages = {
      live_tv: (breakdown.live_tv / total) * 100,
      movies: (breakdown.movies / total) * 100,
      series: (breakdown.series / total) * 100,
      radio: (breakdown.radio / total) * 100
    };

    // Detectar tipo dominante (>60% del contenido)
    if (percentages.series > 60) return 'series';
    if (percentages.movies > 60) return 'movie';
    if (percentages.live_tv > 60) return 'live';
    if (percentages.radio > 60) return 'radio';

    // Si no hay tipo dominante, elegir el mayor
    const maxType = Object.keys(percentages).reduce((a, b) =>
      percentages[a] > percentages[b] ? a : b
    );

    // Mapear nombres
    const typeMap = {
      live_tv: 'live',
      movies: 'movie',
      series: 'series',
      radio: 'radio'
    };

    return typeMap[maxType] || null;
  };

  // Función para obtener etiqueta del content type
  const getContentTypeLabel = (type) => {
    const labels = {
      movie: '🎬 Movies (VOD)',
      series: '📚 TV Series',
      live: '📺 Live Channels',
      radio: '📻 Radio Stations'
    };
    return labels[type] || type;
  };

  // Verificar estado del backend al cargar
  useEffect(() => {
    checkBackendStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Cargar datos cuando el backend status cambie
  useEffect(() => {
    if (backendStatus !== 'checking') {
      loadInitialData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [backendStatus]);

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    setSelectedFile(file);
    setFileAnalysis(null);
    
    if (file) {
      displayAlert('info', `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
      
      // Solo mostrar información básica, el análisis se hace manualmente
      if (window.debugLog) {
        window.debugLog(`📁 File selected: ${file.name}`, 'info');
        window.debugLog(`📊 File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 'info');
      }
    }
  };

  const handleImport = async () => {
    if (!selectedFile || !contentType || !streamsServer) {
      displayAlert('warning', '⚠️ Por favor completa todos los campos requeridos (archivo, tipo de contenido y servidor).');
      return;
    }


    
    if (!fileAnalysis) {
      displayAlert('warning', '⚠️ Por favor analiza el archivo antes de importar.');
      return;
    }
    
    if (window.debugLog) {
      window.debugLog(`📥 Starting import of ${selectedFile.name}`, 'info');
      window.debugLog(`📊 File size: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`, 'info');
      window.debugLog(`🎯 Content type: ${contentType}`, 'info');
      window.debugLog(`🖥️ Target server: ${streamsServer}`, 'info');
    }
    
    setIsImporting(true);
    setImportProgress(0);
    
    try {
      // Preparar configuración de importación
      const importConfig = {
        contentType,
        streamsServer,
        sourceConfig,
        categories: selectedCategories,
        tmdbEnabled: true,
        autoAssignCategories: true
      };
      
      displayAlert('info', '🔍 Iniciando proceso de importación...');
      setImportProgress(10);
      
      // Paso 1: Subir archivo
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('config', JSON.stringify(importConfig));
      
      if (window.debugLog) {
        window.debugLog('📤 Uploading file to backend...', 'info');
      }
      
      // Paso 1: Analizar archivo M3U
      const analyzeResponse = await api.m3uAPI.analyzeFile(selectedFile);
      setImportProgress(30);

      if (!analyzeResponse.success) {
        throw new Error(analyzeResponse.error || 'Error analyzing file');
      }

      displayAlert('info', '🎯 Archivo subido, procesando contenido...');
      // Paso 2: Leer contenido del archivo para parsear episodios
      const fileContent = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = () => reject(new Error('Error reading file'));
        reader.readAsText(selectedFile);
      });

      setImportProgress(40);

      // Paso 3: Parsear contenido del M3U (series o películas)
      const parseResponse = await fetch('http://localhost:5001/api/import/parse-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          m3uContent: fileContent,
          contentType: contentType
        })
      });

      const parseResult = await parseResponse.json();
      if (!parseResult.success) {
        throw new Error(parseResult.error || 'Error parsing content');
      }

      setImportProgress(60);

      // Paso 4: Importar contenido a la base de datos
      const importPayload = {
        server_id: parseInt(streamsServer),
        category_id: selectedCategories.length > 0 ? parseInt(selectedCategories[0]) : null,
        tmdb_search: sourceConfig.tmdbEnrichment || true,
        contentType: contentType
      };

      // Agregar el contenido según el tipo
      if (contentType === 'series') {
        importPayload.episodes = parseResult.data.episodes;
      } else if (contentType === 'movie') {
        importPayload.movies = parseResult.data.movies;
      }

      const importResponse = await fetch('http://localhost:5001/api/import/content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(importPayload)
      });

      const importResult = await importResponse.json();
      if (!importResult.success) {
        throw new Error(importResult.error || 'Error importing content');
      }

      // Paso 5: Finalizar importación
      setImportProgress(100);

      // Mostrar estadísticas según el tipo de contenido
      let successMessage = `✅ Importación completada exitosamente!\n📊 Estadísticas:\n• ${importResult.imported} elementos importados\n• ${importResult.errors} errores`;

      if (contentType === 'series') {
        successMessage += `\n• ${importResult.series_created} series creadas\n• ${importResult.episodes_created} episodios creados`;
      } else if (contentType === 'movie') {
        successMessage += `\n• ${importResult.movies_created} películas creadas`;
      }

      displayAlert('success', successMessage);

      if (window.debugLog) {
        window.debugLog(`✅ Import completed successfully: ${selectedFile.name}`, 'success');
        window.debugLog(`📊 Stats: ${JSON.stringify(stats)}`, 'info');
      }

      // Limpiar estado después de importación exitosa
      setTimeout(() => {
        setSelectedFile(null);
        setFileAnalysis(null);
        setContentType('');
        setStreamsServer('');
        setSelectedCategories([]);
      }, 3000);
      
    } catch (error) {
      console.error('Import error:', error);
      displayAlert('danger', `❌ Error durante la importación: ${error.message}`);
      
      if (window.debugLog) {
        window.debugLog(`❌ Import failed: ${error.message}`, 'error');
      }
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div style={{ width: '100%', maxWidth: 'none' }}>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="text-primary">📥 Import M3U Files</h1>
        <BackendStatus 
          status={backendStatus} 
          onRetry={checkBackendStatus}
        />
      </div>

      {showAlert && (
        <Alert variant={alertType} dismissible onClose={() => setShowAlert(false)}>
          <Alert.Heading>
            {alertType === 'success' && '✅ Success!'}
            {alertType === 'danger' && '❌ Error!'}
            {alertType === 'warning' && '⚠️ Warning!'}
            {alertType === 'info' && 'ℹ️ Information'}
          </Alert.Heading>
          <p>{alertMessage}</p>
        </Alert>
      )}

      <Row className="mb-4">
        <Col lg={6}>
          <Card className="shadow-sm h-100">
            <Card.Header className="bg-primary text-white d-flex justify-content-between align-items-center">
              <h5 className="mb-0">📂 File Upload</h5>
              {backendStatus === 'connected' && (
                <Badge bg="success">
                  <i className="bi bi-cloud-check"></i> Backend Ready
                </Badge>
              )}
            </Card.Header>
            <Card.Body>
              <Form>
                <Form.Group className="mb-3">
                  <Form.Label>Select M3U File</Form.Label>
                  <Form.Control 
                    type="file" 
                    accept=".m3u,.m3u8"
                    onChange={handleFileSelect}
                    disabled={isImporting}
                  />
                  <Form.Text className="text-muted">
                    Supported formats: .m3u, .m3u8 (Max size: 50MB)
                  </Form.Text>
                </Form.Group>

                {selectedFile && (
                  <>
                    <Alert variant="info">
                      <strong>Selected File:</strong> {selectedFile.name}<br/>
                      <strong>Size:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </Alert>
                    
                    {isProcessingFile && (
                      <Alert variant="secondary">
                        <div className="d-flex align-items-center">
                          <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                          Analizando archivo...
                        </div>
                      </Alert>
                    )}
                    
                    {fileAnalysis && !isProcessingFile && (
                      <Alert variant="success">
                        <h6>📊 Análisis del Archivo</h6>
                        <Row>
                          <Col md={6}>
                            <strong>Total Lines:</strong> {fileAnalysis.basic_analysis?.total_lines || 0}<br/>
                            <strong>EXTINF Entries:</strong> {fileAnalysis.basic_analysis?.extinf_lines || 0}<br/>
                            <strong>URL Entries:</strong> {fileAnalysis.basic_analysis?.url_lines || 0}
                          </Col>
                          <Col md={6}>
                            <strong>Estimated Entries:</strong> {fileAnalysis.basic_analysis?.estimated_entries || 0}<br/>
                            <strong>Valid M3U:</strong> {fileAnalysis.basic_analysis?.has_valid_m3u_header ? '✅ Yes' : '❌ No'}<br/>
                            <strong>File Size:</strong> {fileAnalysis.file_info?.size_mb || 0} MB
                          </Col>
                        </Row>
                        
                        {fileAnalysis.parse_results?.series?.success && (
                          <div className="mt-2">
                            <strong>Series Detected:</strong> {fileAnalysis.parse_results.series.data?.length || 0}
                          </div>
                        )}
                      </Alert>
                    )}
                  </>
                )}

                <div className="mb-3">
                  <Button 
                    onClick={handleBrowseFiles} 
                    variant="primary"
                    disabled={isProcessingFile}
                  >
                    Seleccionar Archivo M3U
                  </Button>
                  
                  {selectedFile && !fileAnalysis && (
                    <Button 
                      onClick={handleAnalyzeFile} 
                      variant="info" 
                      className="ms-2"
                      disabled={isProcessingFile}
                    >
                      {isProcessingFile ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                          Analizando...
                        </>
                      ) : (
                        'Analizar Archivo'
                      )}
                    </Button>
                  )}
                  
                  {fileAnalysis && (
                    <Button 
                      onClick={handleClearAnalysis} 
                      variant="outline-secondary" 
                      className="ms-2"
                      disabled={isProcessingFile}
                    >
                      Limpiar Análisis
                    </Button>
                  )}
                </div>

                <Form.Group className="mb-3">
                  <Form.Label>🎯 Content Type</Form.Label>
                  <Form.Select
                    value={contentType}
                    onChange={(e) => setContentType(e.target.value)}
                    disabled={isImporting}
                  >
                    <option value="">Select content type...</option>
                    <option value="movie">🎬 Movies (VOD)</option>
                    <option value="series">📚 TV Series</option>
                    <option value="live">📺 Live Channels</option>
                    <option value="radio">📻 Radio Stations</option>
                  </Form.Select>
                  <Form.Text className="text-muted">
                    This determines how content will be categorized in XUI streams table
                  </Form.Text>
                </Form.Group>

                {contentType && (
                  <Form.Group className="mb-3">
                    <div className="d-flex justify-content-between align-items-center mb-2">
                      <Form.Label className="mb-0">🖥️ Target Streams Server</Form.Label>
                      <Button
                        variant="outline-secondary"
                        size="sm"
                        onClick={loadRealServers}
                        disabled={isImporting || backendStatus !== 'connected'}
                      >
                        🔄 Refresh
                      </Button>
                    </div>
                    <Form.Select
                      value={streamsServer}
                      onChange={(e) => setStreamsServer(e.target.value)}
                      disabled={isImporting}
                    >
                      <option value="">Select streams server...</option>
                      {availableServers.map(server => (
                        <option key={server.id} value={server.id}>
                          {server.name} ({server.ip}) - {server.load}
                        </option>
                      ))}
                    </Form.Select>
                    <Form.Text className="text-muted">
                      Server where streams will be hosted and served from.
                      {availableServers.length > 0 && (
                        <span className="text-success"> ✅ {availableServers.length} servers loaded</span>
                      )}
                    </Form.Text>
                  </Form.Group>
                )}



                {streamsServer && (
                  <>
                    <Form.Group className="mb-3">
                      <Form.Label>🔗 Source Configuration</Form.Label>
                      <Form.Check 
                        type="checkbox" 
                        label="✅ Direct Source (recommended for better performance)"
                        checked={sourceConfig.directSource}
                        onChange={(e) => setSourceConfig(prev => ({
                          ...prev,
                          directSource: e.target.checked
                        }))}
                        disabled={isImporting}
                      />
                      <Form.Check 
                        type="checkbox" 
                        label="🔄 Direct Proxy (for geo-restricted content)"
                        checked={sourceConfig.directProxy}
                        onChange={(e) => setSourceConfig(prev => ({
                          ...prev,
                          directProxy: e.target.checked
                        }))}
                        disabled={isImporting}
                      />
                      <Form.Check 
                        type="checkbox" 
                        label="⚖️ Load Balancing (distribute across servers)"
                        checked={sourceConfig.loadBalancing}
                        onChange={(e) => setSourceConfig(prev => ({
                          ...prev,
                          loadBalancing: e.target.checked
                        }))}
                        disabled={isImporting}
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>🏷️ Categories Assignment</Form.Label>
                      <div style={{maxHeight: '120px', overflowY: 'auto', border: '1px solid #ddd', padding: '8px', borderRadius: '4px'}}>
                        {existingCategories
                          .filter(cat => !contentType || cat.type === contentType || contentType === 'live')
                          .map(category => (
                          <Form.Check
                            key={category.id}
                            type="checkbox"
                            label={`${category.name} (${category.type})`}
                            checked={selectedCategories.includes(category.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedCategories(prev => [...prev, category.id]);
                              } else {
                                setSelectedCategories(prev => prev.filter(id => id !== category.id));
                              }
                            }}
                            disabled={isImporting}
                          />
                        ))}
                      </div>
                      <Form.Text className="text-muted">
                        Select existing categories or new ones will be created automatically
                      </Form.Text>
                    </Form.Group>
                  </>
                )}

                <Form.Group className="mb-3">
                  <Form.Label>⚙️ Import Settings</Form.Label>
                  <Form.Check 
                    type="checkbox" 
                    label="🔄 Auto-rename with TMDB data"
                    defaultChecked
                  />
                  <Form.Check 
                    type="checkbox" 
                    label="📂 Auto-assign categories"
                    defaultChecked
                  />
                  <Form.Check 
                    type="checkbox" 
                    label="🎬 Process VOD metadata"
                    defaultChecked
                  />
                </Form.Group>



                {isImporting && (
                  <div className="mb-3">
                    <Form.Label>Import Progress</Form.Label>
                    <ProgressBar 
                      now={importProgress} 
                      label={`${importProgress}%`}
                      variant={importProgress === 100 ? 'success' : 'primary'}
                      animated={importProgress < 100}
                    />
                  </div>
                )}

                {selectedFile && contentType === 'series' && (
                  <SeriesImportHandler
                    selectedFile={selectedFile}
                    isImporting={isImporting}
                    onSeriesDetected={(series) => {
                      window.debugLog('info', `📺 Detectadas ${series.length} series para importar`);
                    }}
                  />
                )}

                <Button 
                  variant="success" 
                  size="lg" 
                  onClick={handleImport}
                  disabled={!selectedFile || !fileAnalysis || !contentType || !streamsServer || isImporting || isProcessingFile}
                  className="w-100"
                >
                  {isImporting ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                      Importando... {importProgress}%
                    </>
                  ) : !selectedFile ? (
                    '📁 Selecciona un archivo M3U'
                  ) : !fileAnalysis ? (
                    '� Analiza el archivo primero'
                  ) : !contentType || !streamsServer ? (
                    '⚙️ Completa la configuración'
                  ) : (
                    '🚀 Iniciar Importación'
                  )}
                </Button>
              </Form>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={6}>
          <Card className="shadow-sm h-100">
            <Card.Header className="bg-info text-white">
              <h5 className="mb-0">ℹ️ Import Guidelines</h5>
            </Card.Header>
            <Card.Body>
              <h6>📋 Supported Content Types:</h6>
              <ul>
                <li><strong>📺 Live Channels:</strong> TV channels with EPG support</li>
                <li><strong>🎬 VOD (Movies):</strong> On-demand movie content</li>
                <li><strong>📚 Series:</strong> TV series with episode management</li>
                <li><strong>🎵 Radio:</strong> Audio streaming channels</li>
              </ul>

              <h6>⚙️ Processing Features:</h6>
              <ul>
                <li><strong>🎯 TMDB Integration:</strong> Auto-fetch metadata</li>
                <li><strong>🏷️ Category Assignment:</strong> Smart categorization</li>
                <li><strong>🖼️ Poster Download:</strong> High-quality artwork</li>
                <li><strong>📝 Description Parsing:</strong> Extract show info</li>
              </ul>

              <h6>⚡ Performance Tips:</h6>
              <ul>
                <li>Files under 10MB import faster</li>
                <li>Use UTF-8 encoding for special characters</li>
                <li>Clean duplicate entries before import</li>
                <li>Ensure stable internet for metadata fetching</li>
              </ul>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card className="shadow-sm">
            <Card.Header className="bg-secondary text-white">
              <h5 className="mb-0">📊 Recent Import Queue</h5>
            </Card.Header>
            <Card.Body>
              <Table striped hover>
                <thead>
                  <tr>
                    <th>📄 File</th>
                    <th>📅 Queued</th>
                    <th>📊 Status</th>
                    <th>🎯 Target</th>
                    <th>⚡ Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>premium_list.m3u</strong></td>
                    <td>2025-07-15 15:30</td>
                    <td><span className="badge bg-warning">⏳ Queued</span></td>
                    <td>Main Server</td>
                    <td>
                      <Button size="sm" variant="outline-primary" className="me-1">▶️</Button>
                      <Button size="sm" variant="outline-danger">❌</Button>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>sports_channels.m3u</strong></td>
                    <td>2025-07-15 15:25</td>
                    <td><span className="badge bg-success">✅ Processing</span></td>
                    <td>Cloud Server</td>
                    <td>
                      <Button size="sm" variant="outline-info">📊</Button>
                    </td>
                  </tr>
                </tbody>
              </Table>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ImportM3U;
