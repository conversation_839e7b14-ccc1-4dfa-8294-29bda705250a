/**
 * 🗂️ CategorySelector - Selector de Categorías y Bouquets (DEBUG VERSION)
 */

import React from 'react';

const CategorySelector = ({ 
  contentType = 'series', 
  selectedCategory, 
  onCategoryChange,
  selectedBouquet,
  onBouquetChange,
  disabled = false 
}) => {
  console.log('🎯 CategorySelector renderizado con:', { contentType, selectedCategory, selectedBouquet, disabled });
  
  return (
    <div style={{ 
      background: '#f0f8ff', 
      border: '2px solid #007bff', 
      padding: '20px', 
      margin: '10px 0',
      borderRadius: '8px'
    }}>
      <h3>🎯 CategorySelector - FUNCIONANDO!</h3>
      <p><strong>Content Type:</strong> {contentType}</p>
      <p><strong>Selected Category:</strong> {selectedCategory || 'None'}</p>
      <p><strong>Selected Bouquet:</strong> {selectedBouquet || 'None'}</p>
      <p><strong>Disabled:</strong> {disabled ? 'Yes' : 'No'}</p>
      
      <div style={{ marginTop: '15px' }}>
        <label>🎭 Test Bouquet Selector:</label>
        <select 
          value={selectedBouquet || ''} 
          onChange={(e) => onBouquetChange?.(e.target.value)}
          style={{ width: '100%', padding: '8px', margin: '5px 0' }}
        >
          <option value="">Select Bouquet...</option>
          <option value="1">✳️Series</option>
          <option value="2">✳️Peliculas</option>
          <option value="3">✳️Tv en vivo</option>
        </select>
      </div>
      
      <div style={{ marginTop: '15px' }}>
        <label>🗂️ Test Category Selector:</label>
        <select 
          value={selectedCategory || ''} 
          onChange={(e) => onCategoryChange?.(e.target.value)}
          style={{ width: '100%', padding: '8px', margin: '5px 0' }}
        >
          <option value="">Select Category...</option>
          <option value="1">NOVELAS TURCAS</option>
          <option value="2">SERIES ACCION</option>
          <option value="3">DRAMA SERIES</option>
        </select>
      </div>
      
      <div style={{ marginTop: '15px', padding: '10px', background: '#e8f5e8', borderRadius: '4px' }}>
        <strong>✅ Este componente está funcionando correctamente!</strong>
        <br />
        <small>Ahora podemos implementar la funcionalidad completa con los endpoints del backend.</small>
      </div>
    </div>
  );
};

export default CategorySelector;
