@echo off
title XUI IMPORTER - Aplicacion de Escritorio
cls

echo.
echo ========================================
echo     XUI IMPORTER - DESKTOP APP
echo ========================================
echo.

REM Ir al directorio del proyecto
cd /d "F:\WORKSPACE\XUI IMPORTER\xui-importer"

echo Directorio: %CD%
echo.

REM Limpiar procesos anteriores
echo Limpiando procesos anteriores...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Verificar Node.js
echo Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no instalado
    echo Instala Node.js desde: https://nodejs.org/
    pause
    exit
)

echo Node.js OK
echo.

REM Verificar que el backend funcione
echo Verificando backend...
cd backend
node simple-server.js --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Hay problemas con el backend
    echo Revisa el archivo simple-server.js
    pause
    exit
)
cd ..

echo Backend OK
echo.

REM Instalar dependencias si no existen
if not exist "node_modules" (
    echo Instalando dependencias...
    npm install
    if errorlevel 1 (
        echo ERROR: No se pudieron instalar las dependencias
        pause
        exit
    )
    echo.
)

REM Construir la aplicacion React
echo Construyendo aplicacion React...
npm run build
if errorlevel 1 (
    echo ERROR: No se pudo construir la aplicacion
    pause
    exit
)

echo Build completado
echo.

REM Ejecutar la aplicacion de escritorio
echo Iniciando XUI IMPORTER Desktop...
echo.
echo ========================================
echo     APLICACION DE ESCRITORIO INICIADA
echo ========================================
echo.
echo - Se abrira una ventana de escritorio
echo - Backend y Frontend integrados
echo - No necesitas navegador
echo.

npm run electron

echo.
echo Aplicacion cerrada.
pause
