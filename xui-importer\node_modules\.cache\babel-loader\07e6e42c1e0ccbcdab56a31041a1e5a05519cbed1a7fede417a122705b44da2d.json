{"ast": null, "code": "/**\r\n * 🌐 Servicio de API - XUI Importer Frontend\r\n * Cliente para conectar con el backend real\r\n */\n\nimport { getApiBaseUrl, testApiConnectivity } from '../config/apiConfig';\n\n/**\r\n * 🔧 Configuración base para fetch con manejo mejorado de errores\r\n */\nconst apiRequest = async (endpoint, options = {}) => {\n  const url = `${getApiBaseUrl()}${endpoint}`;\n  const defaultOptions = {\n    headers: {\n      'Content-Type': 'application/json',\n      ...options.headers\n    },\n    timeout: 30000 // 30 segundos de timeout\n  };\n  const config = {\n    ...defaultOptions,\n    ...options\n  };\n  try {\n    if (window.debugLog) {\n      window.debugLog('info', `🌐 API Request: ${config.method || 'GET'} ${url}`);\n    } else {\n      console.log(`🌐 API Request: ${config.method || 'GET'} ${url}`);\n    }\n    const response = await fetch(url, config);\n\n    // Verificar si la respuesta es JSON válida\n    let data;\n    try {\n      data = await response.json();\n    } catch (jsonError) {\n      throw new Error(`Respuesta no válida del servidor. Verifique que el backend esté ejecutándose en ${getApiBaseUrl()}`);\n    }\n    if (!response.ok) {\n      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);\n    }\n    if (window.debugLog) {\n      window.debugLog('success', `✅ API Response: ${endpoint}`);\n    } else {\n      console.log(`✅ API Response: ${endpoint}`);\n    }\n    return data;\n  } catch (error) {\n    // Mejorar mensajes de error\n    let errorMessage = error.message;\n    if (error.name === 'TypeError' && error.message.includes('fetch')) {\n      errorMessage = `No se puede conectar al servidor backend. Asegúrese de que el servidor esté ejecutándose en ${getApiBaseUrl()}`;\n    } else if (error.message.includes('Failed to fetch')) {\n      errorMessage = `Error de conexión con el servidor backend en ${getApiBaseUrl()}. Verifique que el servidor esté activo.`;\n    }\n    if (window.debugLog) {\n      window.debugLog('error', `❌ API Error: ${endpoint} - ${errorMessage}`);\n    } else {\n      console.error(`❌ API Error: ${endpoint} - ${errorMessage}`);\n    }\n\n    // Crear error con información adicional\n    const enhancedError = new Error(errorMessage);\n    enhancedError.originalError = error;\n    enhancedError.endpoint = endpoint;\n    enhancedError.url = url;\n    throw enhancedError;\n  }\n};\n\n/**\r\n * 🔥 API de Importación\r\n */\nexport const importAPI = {\n  // Importar series completas\n  importSeries: async importData => {\n    return apiRequest('/import/series', {\n      method: 'POST',\n      body: JSON.stringify(importData)\n    });\n  },\n  // Preview de importación\n  previewSeries: async previewData => {\n    return apiRequest('/import/preview/series', {\n      method: 'POST',\n      body: JSON.stringify(previewData)\n    });\n  },\n  // Resumen de importaciones\n  getImportSummary: async (dateFrom, dateTo) => {\n    const params = new URLSearchParams();\n    if (dateFrom) params.append('date_from', dateFrom);\n    if (dateTo) params.append('date_to', dateTo);\n    return apiRequest(`/import/summary?${params.toString()}`);\n  },\n  // Reprocesar serie\n  reprocessSeries: async (seriesId, options = {}) => {\n    return apiRequest(`/import/reprocess/${seriesId}`, {\n      method: 'POST',\n      body: JSON.stringify(options)\n    });\n  }\n};\n\n/**\r\n * 📺 API de Series\r\n */\nexport const seriesAPI = {\n  // Listar series\n  getSeries: async (page = 1, limit = 20, filters = {}) => {\n    const params = new URLSearchParams({\n      page: page.toString(),\n      limit: limit.toString(),\n      ...filters\n    });\n    return apiRequest(`/series?${params.toString()}`);\n  },\n  // Obtener serie por ID\n  getSeriesById: async id => {\n    return apiRequest(`/series/${id}`);\n  },\n  // Crear nueva serie\n  createSeries: async (seriesData, episodesData) => {\n    return apiRequest('/series', {\n      method: 'POST',\n      body: JSON.stringify({\n        seriesData,\n        episodesData\n      })\n    });\n  },\n  // Actualizar serie\n  updateSeries: async (id, seriesData, episodesData, updateEpisodes = false) => {\n    return apiRequest(`/series/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify({\n        seriesData,\n        episodesData,\n        updateEpisodes\n      })\n    });\n  },\n  // Eliminar serie\n  deleteSeries: async id => {\n    return apiRequest(`/series/${id}`, {\n      method: 'DELETE'\n    });\n  },\n  // Buscar series\n  searchSeries: async (title, tmdbId = null) => {\n    const params = new URLSearchParams({\n      title\n    });\n    if (tmdbId) params.append('tmdb_id', tmdbId);\n    return apiRequest(`/series/search/${encodeURIComponent(title)}?${params.toString()}`);\n  },\n  // Enrichecer con TMDB\n  enrichSeries: async (id, forceUpdate = false) => {\n    return apiRequest(`/series/${id}/enrich`, {\n      method: 'POST',\n      body: JSON.stringify({\n        force_update: forceUpdate\n      })\n    });\n  },\n  // Estadísticas de series\n  getSeriesStats: async () => {\n    return apiRequest('/series/stats/overview');\n  }\n};\n\n/**\r\n * 🗄️ API de Base de Datos\r\n */\nexport const databaseAPI = {\n  // Test de conexión\n  testConnection: async connectionData => {\n    return apiRequest('/database/test', {\n      method: 'POST',\n      body: JSON.stringify(connectionData)\n    });\n  },\n  // Test conexión actual\n  testCurrentConnection: async () => {\n    return apiRequest('/database/test/current');\n  },\n  // Estadísticas de BD\n  getStats: async () => {\n    return apiRequest('/database/stats');\n  },\n  // Verificar tablas\n  verifyTables: async () => {\n    return apiRequest('/database/verify/tables');\n  },\n  // Información de tablas\n  getTablesInfo: async () => {\n    return apiRequest('/database/info/tables');\n  },\n  // Reparar integridad\n  repairIntegrity: async (executeRepair = false) => {\n    return apiRequest('/database/repair/integrity', {\n      method: 'POST',\n      body: JSON.stringify({\n        execute_repair: executeRepair\n      })\n    });\n  },\n  // Limpiar datos de prueba\n  cleanupTestData: async (pattern = 'TEST_%') => {\n    return apiRequest('/database/cleanup/test-data', {\n      method: 'DELETE',\n      body: JSON.stringify({\n        confirm_cleanup: true,\n        test_pattern: pattern\n      })\n    });\n  },\n  // Obtener servidores de streaming\n  getServers: async () => {\n    return apiRequest('/database/servers');\n  },\n  // Obtener categorías\n  getCategories: async () => {\n    return apiRequest('/database/categories');\n  },\n  // 🗂️ Obtener categorías por tipo\n  getCategoriesByType: async (categoryType = null) => {\n    const params = categoryType ? `?type=${categoryType}` : '';\n    return apiRequest(`/database/categories-by-type${params}`);\n  },\n  // 🎭 Obtener bouquets\n  getBouquets: async () => {\n    return apiRequest('/database/bouquets');\n  },\n  // 🎯 Obtener categorías de un bouquet específico\n  getBouquetCategories: async (bouquetId, contentType = 'all') => {\n    return apiRequest(`/database/bouquet/${bouquetId}/categories?content_type=${contentType}`);\n  },\n  // Obtener estadísticas del servidor\n  getServerStats: async () => {\n    return apiRequest('/database/server-stats');\n  },\n  // Obtener datos completos del dashboard\n  getDashboardData: async () => {\n    return apiRequest('/database/dashboard-data');\n  },\n  // 🏯 Obtener tipos de stream (Paso 1)\n  getStreamTypes: async () => {\n    return apiRequest('/database/stream-types');\n  },\n  // 🟢 Obtener muestra de streams (Paso 2)\n  getStreamsSample: async (limit = 100) => {\n    return apiRequest(`/database/streams-sample?limit=${limit}`);\n  },\n  // 🔴 Análisis de estructura de series (Pasos 4A y 4B)\n  getSeriesStructureAnalysis: async () => {\n    return apiRequest('/database/series-structure-analysis');\n  },\n  // 🟢 Obtener streams con paginación completa\n  getStreams: async (page = 1, limit = 1000) => {\n    return apiRequest(`/database/streams?page=${page}&limit=${limit}`);\n  },\n  // 📊 Obtener estadísticas completas de contenido\n  getContentStats: async () => {\n    return apiRequest('/database/content-stats');\n  },\n  // 🚀 Optimizar índices de base de datos\n  optimizeIndexes: async () => {\n    return apiRequest('/database/optimize-indexes', {\n      method: 'POST'\n    });\n  },\n  // 📊 Analizar rendimiento de consultas\n  getQueryPerformance: async () => {\n    return apiRequest('/database/query-performance');\n  }\n};\n\n/**\r\n * 🎬 API de TMDB\r\n */\nexport const tmdbAPI = {\n  // Buscar serie\n  searchSeries: async (title, year = null, page = 1) => {\n    const params = new URLSearchParams({\n      title,\n      page: page.toString()\n    });\n    if (year) params.append('year', year);\n    return apiRequest(`/tmdb/search/series?${params.toString()}`);\n  },\n  // Buscar película\n  searchMovie: async (title, year = null, page = 1) => {\n    const params = new URLSearchParams({\n      title,\n      page: page.toString()\n    });\n    if (year) params.append('year', year);\n    return apiRequest(`/tmdb/search/movie?${params.toString()}`);\n  },\n  // Búsqueda múltiple\n  searchMulti: async (title, year = null) => {\n    const params = new URLSearchParams({\n      title\n    });\n    if (year) params.append('year', year);\n    return apiRequest(`/tmdb/search/multi?${params.toString()}`);\n  },\n  // Detalles de serie\n  getSeriesDetails: async (id, appendToResponse = null) => {\n    const params = new URLSearchParams();\n    if (appendToResponse) params.append('append_to_response', appendToResponse);\n    return apiRequest(`/tmdb/series/${id}?${params.toString()}`);\n  },\n  // Detalles de película\n  getMovieDetails: async (id, appendToResponse = null) => {\n    const params = new URLSearchParams();\n    if (appendToResponse) params.append('append_to_response', appendToResponse);\n    return apiRequest(`/tmdb/movie/${id}?${params.toString()}`);\n  },\n  // Temporadas de serie\n  getSeriesSeasons: async id => {\n    return apiRequest(`/tmdb/series/${id}/seasons`);\n  },\n  // Episodios de temporada\n  getSeasonEpisodes: async (id, seasonNumber) => {\n    return apiRequest(`/tmdb/series/${id}/season/${seasonNumber}`);\n  },\n  // Imágenes de serie\n  getSeriesImages: async id => {\n    return apiRequest(`/tmdb/series/${id}/images`);\n  },\n  // Imágenes de película\n  getMovieImages: async id => {\n    return apiRequest(`/tmdb/movie/${id}/images`);\n  },\n  // URL de imagen\n  getImageUrl: async (path, size = 'w500') => {\n    const params = new URLSearchParams({\n      path,\n      size\n    });\n    return apiRequest(`/tmdb/image/url?${params.toString()}`);\n  },\n  // Formatear para XUI\n  formatForXUI: async (tmdbData, contentType = 'series') => {\n    return apiRequest(`/tmdb/format/${contentType}`, {\n      method: 'POST',\n      body: JSON.stringify({\n        tmdb_data: tmdbData\n      })\n    });\n  },\n  // Búsqueda avanzada\n  searchAdvanced: async searchOptions => {\n    return apiRequest('/tmdb/search/advanced', {\n      method: 'POST',\n      body: JSON.stringify(searchOptions)\n    });\n  },\n  // Test conexión TMDB\n  testConnection: async () => {\n    return apiRequest('/tmdb/test');\n  }\n};\n\n/**\r\n * 📁 API de M3U\r\n */\nexport const m3uAPI = {\n  // Subir archivo\n  uploadFile: async (file, parseType = 'auto', previewOnly = false) => {\n    const formData = new FormData();\n    formData.append('m3uFile', file);\n    formData.append('parse_type', parseType);\n    formData.append('preview_only', previewOnly.toString());\n    return apiRequest('/m3u/upload', {\n      method: 'POST',\n      body: formData,\n      headers: {} // Dejar que el navegador establezca Content-Type\n    });\n  },\n  // Parsear contenido\n  parseContent: async (content, parseType = 'auto', previewOnly = false) => {\n    return apiRequest('/m3u/parse', {\n      method: 'POST',\n      body: JSON.stringify({\n        content,\n        parse_type: parseType,\n        preview_only: previewOnly\n      })\n    });\n  },\n  // Validar M3U\n  validateContent: async content => {\n    return apiRequest('/m3u/validate', {\n      method: 'POST',\n      body: JSON.stringify({\n        content\n      })\n    });\n  },\n  // Analizar estadísticas\n  analyzeContent: async content => {\n    return apiRequest('/m3u/analyze', {\n      method: 'POST',\n      body: JSON.stringify({\n        content\n      })\n    });\n  },\n  // Analizar archivo M3U\n  analyzeFile: async file => {\n    try {\n      // Leer contenido del archivo\n      const content = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = e => resolve(e.target.result);\n        reader.onerror = () => reject(new Error('Error leyendo archivo'));\n        reader.readAsText(file);\n      });\n\n      // Enviar contenido para análisis\n      return apiRequest('/m3u/analyze', {\n        method: 'POST',\n        body: JSON.stringify({\n          content\n        })\n      });\n    } catch (error) {\n      throw new Error(`Error procesando archivo: ${error.message}`);\n    }\n  },\n  // Convertir formato\n  convertContent: async (content, targetFormat = 'xui', includeMetadata = true) => {\n    return apiRequest('/m3u/convert', {\n      method: 'POST',\n      body: JSON.stringify({\n        content,\n        target_format: targetFormat,\n        include_metadata: includeMetadata\n      })\n    });\n  }\n};\n\n/**\r\n * ❤️ API de Sistema\r\n */\nexport const systemAPI = {\n  // Health check\n  getHealth: async () => {\n    return apiRequest('/health');\n  },\n  // Estadísticas generales\n  getStats: async () => {\n    return apiRequest('/stats');\n  }\n};\n\n/**\r\n * 🧪 API de utilidades del sistema\r\n */\nexport const utilsAPI = {\n  // Probar conectividad\n  testConnectivity: async () => {\n    return testApiConnectivity();\n  },\n  // Obtener información de configuración\n  getConfigInfo: async () => {\n    return apiRequest('/system/config');\n  },\n  // Health check\n  healthCheck: async () => {\n    return apiRequest('/health');\n  }\n};\n\n/**\r\n * 🔗 API combinada para facilitar uso\r\n */\nexport const api = {\n  importAPI,\n  seriesAPI,\n  databaseAPI,\n  tmdbAPI,\n  m3uAPI,\n  systemAPI,\n  utilsAPI\n};\nexport default api;", "map": {"version": 3, "names": ["getApiBaseUrl", "testApiConnectivity", "apiRequest", "endpoint", "options", "url", "defaultOptions", "headers", "timeout", "config", "window", "debugLog", "method", "console", "log", "response", "fetch", "data", "json", "jsonError", "Error", "ok", "message", "status", "statusText", "error", "errorMessage", "name", "includes", "enhancedError", "originalError", "importAPI", "importSeries", "importData", "body", "JSON", "stringify", "previewSeries", "previewData", "getImportSummary", "dateFrom", "dateTo", "params", "URLSearchParams", "append", "toString", "reprocessSeries", "seriesId", "seriesAPI", "getSeries", "page", "limit", "filters", "getSeriesById", "id", "createSeries", "seriesData", "episodesData", "updateSeries", "updateEpisodes", "deleteSeries", "searchSeries", "title", "tmdbId", "encodeURIComponent", "enrichSeries", "forceUpdate", "force_update", "getSeriesStats", "databaseAPI", "testConnection", "connectionData", "testCurrentConnection", "getStats", "verifyTables", "getTablesInfo", "repairIntegrity", "executeRepair", "execute_repair", "cleanupTestData", "pattern", "confirm_cleanup", "test_pattern", "getServers", "getCategories", "getCategoriesByType", "categoryType", "getBouquets", "getBouquetCategories", "bouquetId", "contentType", "getServerStats", "getDashboardData", "getStreamTypes", "getStreamsSample", "getSeriesStructureAnalysis", "getStreams", "getContentStats", "optimizeIndexes", "getQueryPerformance", "tmdbAPI", "year", "searchMovie", "searchMulti", "getSeriesDetails", "appendToResponse", "getMovieDetails", "getSeriesSeasons", "getSeasonEpisodes", "seasonNumber", "getSeriesImages", "getMovieImages", "getImageUrl", "path", "size", "formatForXUI", "tmdbData", "tmdb_data", "searchAdvanced", "searchOptions", "m3uAPI", "uploadFile", "file", "parseType", "previewOnly", "formData", "FormData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "parse_type", "preview_only", "validateContent", "analyzeContent", "analyzeFile", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "e", "target", "result", "onerror", "readAsText", "convertContent", "targetFormat", "includeMetadata", "target_format", "include_metadata", "systemAPI", "getHealth", "utilsAPI", "testConnectivity", "getConfigInfo", "healthCheck", "api"], "sources": ["F:/WORKSPACE/XUI IMPORTER/xui-importer/src/services/apiService.js"], "sourcesContent": ["/**\r\n * 🌐 Servicio de API - XUI Importer Frontend\r\n * Cliente para conectar con el backend real\r\n */\r\n\r\nimport { getApiBaseUrl, testApiConnectivity } from '../config/apiConfig';\r\n\r\n/**\r\n * 🔧 Configuración base para fetch con manejo mejorado de errores\r\n */\r\nconst apiRequest = async (endpoint, options = {}) => {\r\n  const url = `${getApiBaseUrl()}${endpoint}`;\r\n\r\n  const defaultOptions = {\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      ...options.headers\r\n    },\r\n    timeout: 30000 // 30 segundos de timeout\r\n  };\r\n\r\n  const config = { ...defaultOptions, ...options };\r\n\r\n  try {\r\n    if (window.debugLog) {\r\n      window.debugLog('info', `🌐 API Request: ${config.method || 'GET'} ${url}`);\r\n    } else {\r\n      console.log(`🌐 API Request: ${config.method || 'GET'} ${url}`);\r\n    }\r\n\r\n    const response = await fetch(url, config);\r\n\r\n    // Verificar si la respuesta es JSON válida\r\n    let data;\r\n    try {\r\n      data = await response.json();\r\n    } catch (jsonError) {\r\n      throw new Error(`Respuesta no válida del servidor. Verifique que el backend esté ejecutándose en ${getApiBaseUrl()}`);\r\n    }\r\n\r\n    if (!response.ok) {\r\n      throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    if (window.debugLog) {\r\n      window.debugLog('success', `✅ API Response: ${endpoint}`);\r\n    } else {\r\n      console.log(`✅ API Response: ${endpoint}`);\r\n    }\r\n\r\n    return data;\r\n\r\n  } catch (error) {\r\n    // Mejorar mensajes de error\r\n    let errorMessage = error.message;\r\n\r\n    if (error.name === 'TypeError' && error.message.includes('fetch')) {\r\n      errorMessage = `No se puede conectar al servidor backend. Asegúrese de que el servidor esté ejecutándose en ${getApiBaseUrl()}`;\r\n    } else if (error.message.includes('Failed to fetch')) {\r\n      errorMessage = `Error de conexión con el servidor backend en ${getApiBaseUrl()}. Verifique que el servidor esté activo.`;\r\n    }\r\n\r\n    if (window.debugLog) {\r\n      window.debugLog('error', `❌ API Error: ${endpoint} - ${errorMessage}`);\r\n    } else {\r\n      console.error(`❌ API Error: ${endpoint} - ${errorMessage}`);\r\n    }\r\n\r\n    // Crear error con información adicional\r\n    const enhancedError = new Error(errorMessage);\r\n    enhancedError.originalError = error;\r\n    enhancedError.endpoint = endpoint;\r\n    enhancedError.url = url;\r\n\r\n    throw enhancedError;\r\n  }\r\n};\r\n\r\n/**\r\n * 🔥 API de Importación\r\n */\r\nexport const importAPI = {\r\n  // Importar series completas\r\n  importSeries: async (importData) => {\r\n    return apiRequest('/import/series', {\r\n      method: 'POST',\r\n      body: JSON.stringify(importData)\r\n    });\r\n  },\r\n\r\n  // Preview de importación\r\n  previewSeries: async (previewData) => {\r\n    return apiRequest('/import/preview/series', {\r\n      method: 'POST',\r\n      body: JSON.stringify(previewData)\r\n    });\r\n  },\r\n\r\n  // Resumen de importaciones\r\n  getImportSummary: async (dateFrom, dateTo) => {\r\n    const params = new URLSearchParams();\r\n    if (dateFrom) params.append('date_from', dateFrom);\r\n    if (dateTo) params.append('date_to', dateTo);\r\n    \r\n    return apiRequest(`/import/summary?${params.toString()}`);\r\n  },\r\n\r\n  // Reprocesar serie\r\n  reprocessSeries: async (seriesId, options = {}) => {\r\n    return apiRequest(`/import/reprocess/${seriesId}`, {\r\n      method: 'POST',\r\n      body: JSON.stringify(options)\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * 📺 API de Series\r\n */\r\nexport const seriesAPI = {\r\n  // Listar series\r\n  getSeries: async (page = 1, limit = 20, filters = {}) => {\r\n    const params = new URLSearchParams({\r\n      page: page.toString(),\r\n      limit: limit.toString(),\r\n      ...filters\r\n    });\r\n    \r\n    return apiRequest(`/series?${params.toString()}`);\r\n  },\r\n\r\n  // Obtener serie por ID\r\n  getSeriesById: async (id) => {\r\n    return apiRequest(`/series/${id}`);\r\n  },\r\n\r\n  // Crear nueva serie\r\n  createSeries: async (seriesData, episodesData) => {\r\n    return apiRequest('/series', {\r\n      method: 'POST',\r\n      body: JSON.stringify({ seriesData, episodesData })\r\n    });\r\n  },\r\n\r\n  // Actualizar serie\r\n  updateSeries: async (id, seriesData, episodesData, updateEpisodes = false) => {\r\n    return apiRequest(`/series/${id}`, {\r\n      method: 'PUT',\r\n      body: JSON.stringify({ seriesData, episodesData, updateEpisodes })\r\n    });\r\n  },\r\n\r\n  // Eliminar serie\r\n  deleteSeries: async (id) => {\r\n    return apiRequest(`/series/${id}`, {\r\n      method: 'DELETE'\r\n    });\r\n  },\r\n\r\n  // Buscar series\r\n  searchSeries: async (title, tmdbId = null) => {\r\n    const params = new URLSearchParams({ title });\r\n    if (tmdbId) params.append('tmdb_id', tmdbId);\r\n    \r\n    return apiRequest(`/series/search/${encodeURIComponent(title)}?${params.toString()}`);\r\n  },\r\n\r\n  // Enrichecer con TMDB\r\n  enrichSeries: async (id, forceUpdate = false) => {\r\n    return apiRequest(`/series/${id}/enrich`, {\r\n      method: 'POST',\r\n      body: JSON.stringify({ force_update: forceUpdate })\r\n    });\r\n  },\r\n\r\n  // Estadísticas de series\r\n  getSeriesStats: async () => {\r\n    return apiRequest('/series/stats/overview');\r\n  }\r\n};\r\n\r\n/**\r\n * 🗄️ API de Base de Datos\r\n */\r\nexport const databaseAPI = {\r\n  // Test de conexión\r\n  testConnection: async (connectionData) => {\r\n    return apiRequest('/database/test', {\r\n      method: 'POST',\r\n      body: JSON.stringify(connectionData)\r\n    });\r\n  },\r\n\r\n  // Test conexión actual\r\n  testCurrentConnection: async () => {\r\n    return apiRequest('/database/test/current');\r\n  },\r\n\r\n  // Estadísticas de BD\r\n  getStats: async () => {\r\n    return apiRequest('/database/stats');\r\n  },\r\n\r\n  // Verificar tablas\r\n  verifyTables: async () => {\r\n    return apiRequest('/database/verify/tables');\r\n  },\r\n\r\n  // Información de tablas\r\n  getTablesInfo: async () => {\r\n    return apiRequest('/database/info/tables');\r\n  },\r\n\r\n  // Reparar integridad\r\n  repairIntegrity: async (executeRepair = false) => {\r\n    return apiRequest('/database/repair/integrity', {\r\n      method: 'POST',\r\n      body: JSON.stringify({ execute_repair: executeRepair })\r\n    });\r\n  },\r\n\r\n  // Limpiar datos de prueba\r\n  cleanupTestData: async (pattern = 'TEST_%') => {\r\n    return apiRequest('/database/cleanup/test-data', {\r\n      method: 'DELETE',\r\n      body: JSON.stringify({ \r\n        confirm_cleanup: true, \r\n        test_pattern: pattern \r\n      })\r\n    });\r\n  },\r\n\r\n  // Obtener servidores de streaming\r\n  getServers: async () => {\r\n    return apiRequest('/database/servers');\r\n  },\r\n\r\n  // Obtener categorías\r\n  getCategories: async () => {\r\n    return apiRequest('/database/categories');\r\n  },\r\n\r\n  // 🗂️ Obtener categorías por tipo\r\n  getCategoriesByType: async (categoryType = null) => {\r\n    const params = categoryType ? `?type=${categoryType}` : '';\r\n    return apiRequest(`/database/categories-by-type${params}`);\r\n  },\r\n\r\n  // 🎭 Obtener bouquets\r\n  getBouquets: async () => {\r\n    return apiRequest('/database/bouquets');\r\n  },\r\n\r\n  // 🎯 Obtener categorías de un bouquet específico\r\n  getBouquetCategories: async (bouquetId, contentType = 'all') => {\r\n    return apiRequest(`/database/bouquet/${bouquetId}/categories?content_type=${contentType}`);\r\n  },\r\n\r\n  // Obtener estadísticas del servidor\r\n  getServerStats: async () => {\r\n    return apiRequest('/database/server-stats');\r\n  },\r\n\r\n  // Obtener datos completos del dashboard\r\n  getDashboardData: async () => {\r\n    return apiRequest('/database/dashboard-data');\r\n  },\r\n\r\n  // 🏯 Obtener tipos de stream (Paso 1)\r\n  getStreamTypes: async () => {\r\n    return apiRequest('/database/stream-types');\r\n  },\r\n\r\n  // 🟢 Obtener muestra de streams (Paso 2)\r\n  getStreamsSample: async (limit = 100) => {\r\n    return apiRequest(`/database/streams-sample?limit=${limit}`);\r\n  },\r\n\r\n  // 🔴 Análisis de estructura de series (Pasos 4A y 4B)\r\n  getSeriesStructureAnalysis: async () => {\r\n    return apiRequest('/database/series-structure-analysis');\r\n  },\r\n\r\n  // 🟢 Obtener streams con paginación completa\r\n  getStreams: async (page = 1, limit = 1000) => {\r\n    return apiRequest(`/database/streams?page=${page}&limit=${limit}`);\r\n  },\r\n\r\n  // 📊 Obtener estadísticas completas de contenido\r\n  getContentStats: async () => {\r\n    return apiRequest('/database/content-stats');\r\n  },\r\n\r\n  // 🚀 Optimizar índices de base de datos\r\n  optimizeIndexes: async () => {\r\n    return apiRequest('/database/optimize-indexes', {\r\n      method: 'POST'\r\n    });\r\n  },\r\n\r\n  // 📊 Analizar rendimiento de consultas\r\n  getQueryPerformance: async () => {\r\n    return apiRequest('/database/query-performance');\r\n  }\r\n};\r\n\r\n/**\r\n * 🎬 API de TMDB\r\n */\r\nexport const tmdbAPI = {\r\n  // Buscar serie\r\n  searchSeries: async (title, year = null, page = 1) => {\r\n    const params = new URLSearchParams({ title, page: page.toString() });\r\n    if (year) params.append('year', year);\r\n    \r\n    return apiRequest(`/tmdb/search/series?${params.toString()}`);\r\n  },\r\n\r\n  // Buscar película\r\n  searchMovie: async (title, year = null, page = 1) => {\r\n    const params = new URLSearchParams({ title, page: page.toString() });\r\n    if (year) params.append('year', year);\r\n    \r\n    return apiRequest(`/tmdb/search/movie?${params.toString()}`);\r\n  },\r\n\r\n  // Búsqueda múltiple\r\n  searchMulti: async (title, year = null) => {\r\n    const params = new URLSearchParams({ title });\r\n    if (year) params.append('year', year);\r\n    \r\n    return apiRequest(`/tmdb/search/multi?${params.toString()}`);\r\n  },\r\n\r\n  // Detalles de serie\r\n  getSeriesDetails: async (id, appendToResponse = null) => {\r\n    const params = new URLSearchParams();\r\n    if (appendToResponse) params.append('append_to_response', appendToResponse);\r\n    \r\n    return apiRequest(`/tmdb/series/${id}?${params.toString()}`);\r\n  },\r\n\r\n  // Detalles de película\r\n  getMovieDetails: async (id, appendToResponse = null) => {\r\n    const params = new URLSearchParams();\r\n    if (appendToResponse) params.append('append_to_response', appendToResponse);\r\n    \r\n    return apiRequest(`/tmdb/movie/${id}?${params.toString()}`);\r\n  },\r\n\r\n  // Temporadas de serie\r\n  getSeriesSeasons: async (id) => {\r\n    return apiRequest(`/tmdb/series/${id}/seasons`);\r\n  },\r\n\r\n  // Episodios de temporada\r\n  getSeasonEpisodes: async (id, seasonNumber) => {\r\n    return apiRequest(`/tmdb/series/${id}/season/${seasonNumber}`);\r\n  },\r\n\r\n  // Imágenes de serie\r\n  getSeriesImages: async (id) => {\r\n    return apiRequest(`/tmdb/series/${id}/images`);\r\n  },\r\n\r\n  // Imágenes de película\r\n  getMovieImages: async (id) => {\r\n    return apiRequest(`/tmdb/movie/${id}/images`);\r\n  },\r\n\r\n  // URL de imagen\r\n  getImageUrl: async (path, size = 'w500') => {\r\n    const params = new URLSearchParams({ path, size });\r\n    return apiRequest(`/tmdb/image/url?${params.toString()}`);\r\n  },\r\n\r\n  // Formatear para XUI\r\n  formatForXUI: async (tmdbData, contentType = 'series') => {\r\n    return apiRequest(`/tmdb/format/${contentType}`, {\r\n      method: 'POST',\r\n      body: JSON.stringify({ tmdb_data: tmdbData })\r\n    });\r\n  },\r\n\r\n  // Búsqueda avanzada\r\n  searchAdvanced: async (searchOptions) => {\r\n    return apiRequest('/tmdb/search/advanced', {\r\n      method: 'POST',\r\n      body: JSON.stringify(searchOptions)\r\n    });\r\n  },\r\n\r\n  // Test conexión TMDB\r\n  testConnection: async () => {\r\n    return apiRequest('/tmdb/test');\r\n  }\r\n};\r\n\r\n/**\r\n * 📁 API de M3U\r\n */\r\nexport const m3uAPI = {\r\n  // Subir archivo\r\n  uploadFile: async (file, parseType = 'auto', previewOnly = false) => {\r\n    const formData = new FormData();\r\n    formData.append('m3uFile', file);\r\n    formData.append('parse_type', parseType);\r\n    formData.append('preview_only', previewOnly.toString());\r\n    \r\n    return apiRequest('/m3u/upload', {\r\n      method: 'POST',\r\n      body: formData,\r\n      headers: {} // Dejar que el navegador establezca Content-Type\r\n    });\r\n  },\r\n\r\n  // Parsear contenido\r\n  parseContent: async (content, parseType = 'auto', previewOnly = false) => {\r\n    return apiRequest('/m3u/parse', {\r\n      method: 'POST',\r\n      body: JSON.stringify({ content, parse_type: parseType, preview_only: previewOnly })\r\n    });\r\n  },\r\n\r\n  // Validar M3U\r\n  validateContent: async (content) => {\r\n    return apiRequest('/m3u/validate', {\r\n      method: 'POST',\r\n      body: JSON.stringify({ content })\r\n    });\r\n  },\r\n\r\n  // Analizar estadísticas\r\n  analyzeContent: async (content) => {\r\n    return apiRequest('/m3u/analyze', {\r\n      method: 'POST',\r\n      body: JSON.stringify({ content })\r\n    });\r\n  },\r\n\r\n  // Analizar archivo M3U\r\n  analyzeFile: async (file) => {\r\n    try {\r\n      // Leer contenido del archivo\r\n      const content = await new Promise((resolve, reject) => {\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => resolve(e.target.result);\r\n        reader.onerror = () => reject(new Error('Error leyendo archivo'));\r\n        reader.readAsText(file);\r\n      });\r\n\r\n      // Enviar contenido para análisis\r\n      return apiRequest('/m3u/analyze', {\r\n        method: 'POST',\r\n        body: JSON.stringify({ content })\r\n      });\r\n    } catch (error) {\r\n      throw new Error(`Error procesando archivo: ${error.message}`);\r\n    }\r\n  },\r\n\r\n  // Convertir formato\r\n  convertContent: async (content, targetFormat = 'xui', includeMetadata = true) => {\r\n    return apiRequest('/m3u/convert', {\r\n      method: 'POST',\r\n      body: JSON.stringify({ \r\n        content, \r\n        target_format: targetFormat, \r\n        include_metadata: includeMetadata \r\n      })\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * ❤️ API de Sistema\r\n */\r\nexport const systemAPI = {\r\n  // Health check\r\n  getHealth: async () => {\r\n    return apiRequest('/health');\r\n  },\r\n\r\n  // Estadísticas generales\r\n  getStats: async () => {\r\n    return apiRequest('/stats');\r\n  }\r\n};\r\n\r\n/**\r\n * 🧪 API de utilidades del sistema\r\n */\r\nexport const utilsAPI = {\r\n  // Probar conectividad\r\n  testConnectivity: async () => {\r\n    return testApiConnectivity();\r\n  },\r\n\r\n  // Obtener información de configuración\r\n  getConfigInfo: async () => {\r\n    return apiRequest('/system/config');\r\n  },\r\n\r\n  // Health check\r\n  healthCheck: async () => {\r\n    return apiRequest('/health');\r\n  }\r\n};\r\n\r\n/**\r\n * 🔗 API combinada para facilitar uso\r\n */\r\nexport const api = {\r\n  importAPI,\r\n  seriesAPI,\r\n  databaseAPI,\r\n  tmdbAPI,\r\n  m3uAPI,\r\n  systemAPI,\r\n  utilsAPI\r\n};\r\n\r\nexport default api;\r\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,aAAa,EAAEC,mBAAmB,QAAQ,qBAAqB;;AAExE;AACA;AACA;AACA,MAAMC,UAAU,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACnD,MAAMC,GAAG,GAAG,GAAGL,aAAa,CAAC,CAAC,GAAGG,QAAQ,EAAE;EAE3C,MAAMG,cAAc,GAAG;IACrBC,OAAO,EAAE;MACP,cAAc,EAAE,kBAAkB;MAClC,GAAGH,OAAO,CAACG;IACb,CAAC;IACDC,OAAO,EAAE,KAAK,CAAC;EACjB,CAAC;EAED,MAAMC,MAAM,GAAG;IAAE,GAAGH,cAAc;IAAE,GAAGF;EAAQ,CAAC;EAEhD,IAAI;IACF,IAAIM,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,MAAM,EAAE,mBAAmBF,MAAM,CAACG,MAAM,IAAI,KAAK,IAAIP,GAAG,EAAE,CAAC;IAC7E,CAAC,MAAM;MACLQ,OAAO,CAACC,GAAG,CAAC,mBAAmBL,MAAM,CAACG,MAAM,IAAI,KAAK,IAAIP,GAAG,EAAE,CAAC;IACjE;IAEA,MAAMU,QAAQ,GAAG,MAAMC,KAAK,CAACX,GAAG,EAAEI,MAAM,CAAC;;IAEzC;IACA,IAAIQ,IAAI;IACR,IAAI;MACFA,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOC,SAAS,EAAE;MAClB,MAAM,IAAIC,KAAK,CAAC,mFAAmFpB,aAAa,CAAC,CAAC,EAAE,CAAC;IACvH;IAEA,IAAI,CAACe,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAID,KAAK,CAACH,IAAI,CAACK,OAAO,IAAI,QAAQP,QAAQ,CAACQ,MAAM,KAAKR,QAAQ,CAACS,UAAU,EAAE,CAAC;IACpF;IAEA,IAAId,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,mBAAmBR,QAAQ,EAAE,CAAC;IAC3D,CAAC,MAAM;MACLU,OAAO,CAACC,GAAG,CAAC,mBAAmBX,QAAQ,EAAE,CAAC;IAC5C;IAEA,OAAOc,IAAI;EAEb,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACd;IACA,IAAIC,YAAY,GAAGD,KAAK,CAACH,OAAO;IAEhC,IAAIG,KAAK,CAACE,IAAI,KAAK,WAAW,IAAIF,KAAK,CAACH,OAAO,CAACM,QAAQ,CAAC,OAAO,CAAC,EAAE;MACjEF,YAAY,GAAG,+FAA+F1B,aAAa,CAAC,CAAC,EAAE;IACjI,CAAC,MAAM,IAAIyB,KAAK,CAACH,OAAO,CAACM,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MACpDF,YAAY,GAAG,gDAAgD1B,aAAa,CAAC,CAAC,0CAA0C;IAC1H;IAEA,IAAIU,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,gBAAgBR,QAAQ,MAAMuB,YAAY,EAAE,CAAC;IACxE,CAAC,MAAM;MACLb,OAAO,CAACY,KAAK,CAAC,gBAAgBtB,QAAQ,MAAMuB,YAAY,EAAE,CAAC;IAC7D;;IAEA;IACA,MAAMG,aAAa,GAAG,IAAIT,KAAK,CAACM,YAAY,CAAC;IAC7CG,aAAa,CAACC,aAAa,GAAGL,KAAK;IACnCI,aAAa,CAAC1B,QAAQ,GAAGA,QAAQ;IACjC0B,aAAa,CAACxB,GAAG,GAAGA,GAAG;IAEvB,MAAMwB,aAAa;EACrB;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,SAAS,GAAG;EACvB;EACAC,YAAY,EAAE,MAAOC,UAAU,IAAK;IAClC,OAAO/B,UAAU,CAAC,gBAAgB,EAAE;MAClCU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACH,UAAU;IACjC,CAAC,CAAC;EACJ,CAAC;EAED;EACAI,aAAa,EAAE,MAAOC,WAAW,IAAK;IACpC,OAAOpC,UAAU,CAAC,wBAAwB,EAAE;MAC1CU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACE,WAAW;IAClC,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,gBAAgB,EAAE,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,KAAK;IAC5C,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIH,QAAQ,EAAEE,MAAM,CAACE,MAAM,CAAC,WAAW,EAAEJ,QAAQ,CAAC;IAClD,IAAIC,MAAM,EAAEC,MAAM,CAACE,MAAM,CAAC,SAAS,EAAEH,MAAM,CAAC;IAE5C,OAAOvC,UAAU,CAAC,mBAAmBwC,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC3D,CAAC;EAED;EACAC,eAAe,EAAE,MAAAA,CAAOC,QAAQ,EAAE3C,OAAO,GAAG,CAAC,CAAC,KAAK;IACjD,OAAOF,UAAU,CAAC,qBAAqB6C,QAAQ,EAAE,EAAE;MACjDnC,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAChC,OAAO;IAC9B,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM4C,SAAS,GAAG;EACvB;EACAC,SAAS,EAAE,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IACvD,MAAMV,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCO,IAAI,EAAEA,IAAI,CAACL,QAAQ,CAAC,CAAC;MACrBM,KAAK,EAAEA,KAAK,CAACN,QAAQ,CAAC,CAAC;MACvB,GAAGO;IACL,CAAC,CAAC;IAEF,OAAOlD,UAAU,CAAC,WAAWwC,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;EACnD,CAAC;EAED;EACAQ,aAAa,EAAE,MAAOC,EAAE,IAAK;IAC3B,OAAOpD,UAAU,CAAC,WAAWoD,EAAE,EAAE,CAAC;EACpC,CAAC;EAED;EACAC,YAAY,EAAE,MAAAA,CAAOC,UAAU,EAAEC,YAAY,KAAK;IAChD,OAAOvD,UAAU,CAAC,SAAS,EAAE;MAC3BU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEoB,UAAU;QAAEC;MAAa,CAAC;IACnD,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,YAAY,EAAE,MAAAA,CAAOJ,EAAE,EAAEE,UAAU,EAAEC,YAAY,EAAEE,cAAc,GAAG,KAAK,KAAK;IAC5E,OAAOzD,UAAU,CAAC,WAAWoD,EAAE,EAAE,EAAE;MACjC1C,MAAM,EAAE,KAAK;MACbsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEoB,UAAU;QAAEC,YAAY;QAAEE;MAAe,CAAC;IACnE,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,YAAY,EAAE,MAAON,EAAE,IAAK;IAC1B,OAAOpD,UAAU,CAAC,WAAWoD,EAAE,EAAE,EAAE;MACjC1C,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;EACAiD,YAAY,EAAE,MAAAA,CAAOC,KAAK,EAAEC,MAAM,GAAG,IAAI,KAAK;IAC5C,MAAMrB,MAAM,GAAG,IAAIC,eAAe,CAAC;MAAEmB;IAAM,CAAC,CAAC;IAC7C,IAAIC,MAAM,EAAErB,MAAM,CAACE,MAAM,CAAC,SAAS,EAAEmB,MAAM,CAAC;IAE5C,OAAO7D,UAAU,CAAC,kBAAkB8D,kBAAkB,CAACF,KAAK,CAAC,IAAIpB,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;EACvF,CAAC;EAED;EACAoB,YAAY,EAAE,MAAAA,CAAOX,EAAE,EAAEY,WAAW,GAAG,KAAK,KAAK;IAC/C,OAAOhE,UAAU,CAAC,WAAWoD,EAAE,SAAS,EAAE;MACxC1C,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE+B,YAAY,EAAED;MAAY,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC;EAED;EACAE,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,OAAOlE,UAAU,CAAC,wBAAwB,CAAC;EAC7C;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMmE,WAAW,GAAG;EACzB;EACAC,cAAc,EAAE,MAAOC,cAAc,IAAK;IACxC,OAAOrE,UAAU,CAAC,gBAAgB,EAAE;MAClCU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACmC,cAAc;IACrC,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,qBAAqB,EAAE,MAAAA,CAAA,KAAY;IACjC,OAAOtE,UAAU,CAAC,wBAAwB,CAAC;EAC7C,CAAC;EAED;EACAuE,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpB,OAAOvE,UAAU,CAAC,iBAAiB,CAAC;EACtC,CAAC;EAED;EACAwE,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,OAAOxE,UAAU,CAAC,yBAAyB,CAAC;EAC9C,CAAC;EAED;EACAyE,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,OAAOzE,UAAU,CAAC,uBAAuB,CAAC;EAC5C,CAAC;EAED;EACA0E,eAAe,EAAE,MAAAA,CAAOC,aAAa,GAAG,KAAK,KAAK;IAChD,OAAO3E,UAAU,CAAC,4BAA4B,EAAE;MAC9CU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE0C,cAAc,EAAED;MAAc,CAAC;IACxD,CAAC,CAAC;EACJ,CAAC;EAED;EACAE,eAAe,EAAE,MAAAA,CAAOC,OAAO,GAAG,QAAQ,KAAK;IAC7C,OAAO9E,UAAU,CAAC,6BAA6B,EAAE;MAC/CU,MAAM,EAAE,QAAQ;MAChBsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnB6C,eAAe,EAAE,IAAI;QACrBC,YAAY,EAAEF;MAChB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED;EACAG,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,OAAOjF,UAAU,CAAC,mBAAmB,CAAC;EACxC,CAAC;EAED;EACAkF,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,OAAOlF,UAAU,CAAC,sBAAsB,CAAC;EAC3C,CAAC;EAED;EACAmF,mBAAmB,EAAE,MAAAA,CAAOC,YAAY,GAAG,IAAI,KAAK;IAClD,MAAM5C,MAAM,GAAG4C,YAAY,GAAG,SAASA,YAAY,EAAE,GAAG,EAAE;IAC1D,OAAOpF,UAAU,CAAC,+BAA+BwC,MAAM,EAAE,CAAC;EAC5D,CAAC;EAED;EACA6C,WAAW,EAAE,MAAAA,CAAA,KAAY;IACvB,OAAOrF,UAAU,CAAC,oBAAoB,CAAC;EACzC,CAAC;EAED;EACAsF,oBAAoB,EAAE,MAAAA,CAAOC,SAAS,EAAEC,WAAW,GAAG,KAAK,KAAK;IAC9D,OAAOxF,UAAU,CAAC,qBAAqBuF,SAAS,4BAA4BC,WAAW,EAAE,CAAC;EAC5F,CAAC;EAED;EACAC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,OAAOzF,UAAU,CAAC,wBAAwB,CAAC;EAC7C,CAAC;EAED;EACA0F,gBAAgB,EAAE,MAAAA,CAAA,KAAY;IAC5B,OAAO1F,UAAU,CAAC,0BAA0B,CAAC;EAC/C,CAAC;EAED;EACA2F,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,OAAO3F,UAAU,CAAC,wBAAwB,CAAC;EAC7C,CAAC;EAED;EACA4F,gBAAgB,EAAE,MAAAA,CAAO3C,KAAK,GAAG,GAAG,KAAK;IACvC,OAAOjD,UAAU,CAAC,kCAAkCiD,KAAK,EAAE,CAAC;EAC9D,CAAC;EAED;EACA4C,0BAA0B,EAAE,MAAAA,CAAA,KAAY;IACtC,OAAO7F,UAAU,CAAC,qCAAqC,CAAC;EAC1D,CAAC;EAED;EACA8F,UAAU,EAAE,MAAAA,CAAO9C,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,IAAI,KAAK;IAC5C,OAAOjD,UAAU,CAAC,0BAA0BgD,IAAI,UAAUC,KAAK,EAAE,CAAC;EACpE,CAAC;EAED;EACA8C,eAAe,EAAE,MAAAA,CAAA,KAAY;IAC3B,OAAO/F,UAAU,CAAC,yBAAyB,CAAC;EAC9C,CAAC;EAED;EACAgG,eAAe,EAAE,MAAAA,CAAA,KAAY;IAC3B,OAAOhG,UAAU,CAAC,4BAA4B,EAAE;MAC9CU,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;EACAuF,mBAAmB,EAAE,MAAAA,CAAA,KAAY;IAC/B,OAAOjG,UAAU,CAAC,6BAA6B,CAAC;EAClD;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMkG,OAAO,GAAG;EACrB;EACAvC,YAAY,EAAE,MAAAA,CAAOC,KAAK,EAAEuC,IAAI,GAAG,IAAI,EAAEnD,IAAI,GAAG,CAAC,KAAK;IACpD,MAAMR,MAAM,GAAG,IAAIC,eAAe,CAAC;MAAEmB,KAAK;MAAEZ,IAAI,EAAEA,IAAI,CAACL,QAAQ,CAAC;IAAE,CAAC,CAAC;IACpE,IAAIwD,IAAI,EAAE3D,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEyD,IAAI,CAAC;IAErC,OAAOnG,UAAU,CAAC,uBAAuBwC,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC/D,CAAC;EAED;EACAyD,WAAW,EAAE,MAAAA,CAAOxC,KAAK,EAAEuC,IAAI,GAAG,IAAI,EAAEnD,IAAI,GAAG,CAAC,KAAK;IACnD,MAAMR,MAAM,GAAG,IAAIC,eAAe,CAAC;MAAEmB,KAAK;MAAEZ,IAAI,EAAEA,IAAI,CAACL,QAAQ,CAAC;IAAE,CAAC,CAAC;IACpE,IAAIwD,IAAI,EAAE3D,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEyD,IAAI,CAAC;IAErC,OAAOnG,UAAU,CAAC,sBAAsBwC,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC9D,CAAC;EAED;EACA0D,WAAW,EAAE,MAAAA,CAAOzC,KAAK,EAAEuC,IAAI,GAAG,IAAI,KAAK;IACzC,MAAM3D,MAAM,GAAG,IAAIC,eAAe,CAAC;MAAEmB;IAAM,CAAC,CAAC;IAC7C,IAAIuC,IAAI,EAAE3D,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEyD,IAAI,CAAC;IAErC,OAAOnG,UAAU,CAAC,sBAAsBwC,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC9D,CAAC;EAED;EACA2D,gBAAgB,EAAE,MAAAA,CAAOlD,EAAE,EAAEmD,gBAAgB,GAAG,IAAI,KAAK;IACvD,MAAM/D,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAI8D,gBAAgB,EAAE/D,MAAM,CAACE,MAAM,CAAC,oBAAoB,EAAE6D,gBAAgB,CAAC;IAE3E,OAAOvG,UAAU,CAAC,gBAAgBoD,EAAE,IAAIZ,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC9D,CAAC;EAED;EACA6D,eAAe,EAAE,MAAAA,CAAOpD,EAAE,EAAEmD,gBAAgB,GAAG,IAAI,KAAK;IACtD,MAAM/D,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAI8D,gBAAgB,EAAE/D,MAAM,CAACE,MAAM,CAAC,oBAAoB,EAAE6D,gBAAgB,CAAC;IAE3E,OAAOvG,UAAU,CAAC,eAAeoD,EAAE,IAAIZ,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC7D,CAAC;EAED;EACA8D,gBAAgB,EAAE,MAAOrD,EAAE,IAAK;IAC9B,OAAOpD,UAAU,CAAC,gBAAgBoD,EAAE,UAAU,CAAC;EACjD,CAAC;EAED;EACAsD,iBAAiB,EAAE,MAAAA,CAAOtD,EAAE,EAAEuD,YAAY,KAAK;IAC7C,OAAO3G,UAAU,CAAC,gBAAgBoD,EAAE,WAAWuD,YAAY,EAAE,CAAC;EAChE,CAAC;EAED;EACAC,eAAe,EAAE,MAAOxD,EAAE,IAAK;IAC7B,OAAOpD,UAAU,CAAC,gBAAgBoD,EAAE,SAAS,CAAC;EAChD,CAAC;EAED;EACAyD,cAAc,EAAE,MAAOzD,EAAE,IAAK;IAC5B,OAAOpD,UAAU,CAAC,eAAeoD,EAAE,SAAS,CAAC;EAC/C,CAAC;EAED;EACA0D,WAAW,EAAE,MAAAA,CAAOC,IAAI,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC1C,MAAMxE,MAAM,GAAG,IAAIC,eAAe,CAAC;MAAEsE,IAAI;MAAEC;IAAK,CAAC,CAAC;IAClD,OAAOhH,UAAU,CAAC,mBAAmBwC,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC3D,CAAC;EAED;EACAsE,YAAY,EAAE,MAAAA,CAAOC,QAAQ,EAAE1B,WAAW,GAAG,QAAQ,KAAK;IACxD,OAAOxF,UAAU,CAAC,gBAAgBwF,WAAW,EAAE,EAAE;MAC/C9E,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEiF,SAAS,EAAED;MAAS,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC;EAED;EACAE,cAAc,EAAE,MAAOC,aAAa,IAAK;IACvC,OAAOrH,UAAU,CAAC,uBAAuB,EAAE;MACzCU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACmF,aAAa;IACpC,CAAC,CAAC;EACJ,CAAC;EAED;EACAjD,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,OAAOpE,UAAU,CAAC,YAAY,CAAC;EACjC;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMsH,MAAM,GAAG;EACpB;EACAC,UAAU,EAAE,MAAAA,CAAOC,IAAI,EAAEC,SAAS,GAAG,MAAM,EAAEC,WAAW,GAAG,KAAK,KAAK;IACnE,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACjF,MAAM,CAAC,SAAS,EAAE8E,IAAI,CAAC;IAChCG,QAAQ,CAACjF,MAAM,CAAC,YAAY,EAAE+E,SAAS,CAAC;IACxCE,QAAQ,CAACjF,MAAM,CAAC,cAAc,EAAEgF,WAAW,CAAC/E,QAAQ,CAAC,CAAC,CAAC;IAEvD,OAAO3C,UAAU,CAAC,aAAa,EAAE;MAC/BU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAE2F,QAAQ;MACdtH,OAAO,EAAE,CAAC,CAAC,CAAC;IACd,CAAC,CAAC;EACJ,CAAC;EAED;EACAwH,YAAY,EAAE,MAAAA,CAAOC,OAAO,EAAEL,SAAS,GAAG,MAAM,EAAEC,WAAW,GAAG,KAAK,KAAK;IACxE,OAAO1H,UAAU,CAAC,YAAY,EAAE;MAC9BU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE4F,OAAO;QAAEC,UAAU,EAAEN,SAAS;QAAEO,YAAY,EAAEN;MAAY,CAAC;IACpF,CAAC,CAAC;EACJ,CAAC;EAED;EACAO,eAAe,EAAE,MAAOH,OAAO,IAAK;IAClC,OAAO9H,UAAU,CAAC,eAAe,EAAE;MACjCU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE4F;MAAQ,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EAED;EACAI,cAAc,EAAE,MAAOJ,OAAO,IAAK;IACjC,OAAO9H,UAAU,CAAC,cAAc,EAAE;MAChCU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAE4F;MAAQ,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EAED;EACAK,WAAW,EAAE,MAAOX,IAAI,IAAK;IAC3B,IAAI;MACF;MACA,MAAMM,OAAO,GAAG,MAAM,IAAIM,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACrD,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKL,OAAO,CAACK,CAAC,CAACC,MAAM,CAACC,MAAM,CAAC;QAC/CL,MAAM,CAACM,OAAO,GAAG,MAAMP,MAAM,CAAC,IAAIpH,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACjEqH,MAAM,CAACO,UAAU,CAACtB,IAAI,CAAC;MACzB,CAAC,CAAC;;MAEF;MACA,OAAOxH,UAAU,CAAC,cAAc,EAAE;QAChCU,MAAM,EAAE,MAAM;QACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAE4F;QAAQ,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvG,KAAK,EAAE;MACd,MAAM,IAAIL,KAAK,CAAC,6BAA6BK,KAAK,CAACH,OAAO,EAAE,CAAC;IAC/D;EACF,CAAC;EAED;EACA2H,cAAc,EAAE,MAAAA,CAAOjB,OAAO,EAAEkB,YAAY,GAAG,KAAK,EAAEC,eAAe,GAAG,IAAI,KAAK;IAC/E,OAAOjJ,UAAU,CAAC,cAAc,EAAE;MAChCU,MAAM,EAAE,MAAM;MACdsB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnB4F,OAAO;QACPoB,aAAa,EAAEF,YAAY;QAC3BG,gBAAgB,EAAEF;MACpB,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMG,SAAS,GAAG;EACvB;EACAC,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,OAAOrJ,UAAU,CAAC,SAAS,CAAC;EAC9B,CAAC;EAED;EACAuE,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpB,OAAOvE,UAAU,CAAC,QAAQ,CAAC;EAC7B;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMsJ,QAAQ,GAAG;EACtB;EACAC,gBAAgB,EAAE,MAAAA,CAAA,KAAY;IAC5B,OAAOxJ,mBAAmB,CAAC,CAAC;EAC9B,CAAC;EAED;EACAyJ,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,OAAOxJ,UAAU,CAAC,gBAAgB,CAAC;EACrC,CAAC;EAED;EACAyJ,WAAW,EAAE,MAAAA,CAAA,KAAY;IACvB,OAAOzJ,UAAU,CAAC,SAAS,CAAC;EAC9B;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM0J,GAAG,GAAG;EACjB7H,SAAS;EACTiB,SAAS;EACTqB,WAAW;EACX+B,OAAO;EACPoB,MAAM;EACN8B,SAAS;EACTE;AACF,CAAC;AAED,eAAeI,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}