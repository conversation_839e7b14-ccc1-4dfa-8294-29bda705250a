{"ast": null, "code": "var _jsxFileName = \"F:\\\\WORKSPACE\\\\XUI IMPORTER\\\\xui-importer\\\\src\\\\components\\\\ImportM3U.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Button, Form, Alert, ProgressBar, Table, Badge } from 'react-bootstrap';\nimport SeriesImportHandler from './SeriesImportHandler';\nimport BackendStatus from './BackendStatus';\nimport { checkSystemHealth } from '../utils/seriesLogic';\nimport { api, databaseAPI } from '../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImportM3U = () => {\n  _s();\n  var _fileAnalysis$basic_a, _fileAnalysis$basic_a2, _fileAnalysis$basic_a3, _fileAnalysis$basic_a4, _fileAnalysis$basic_a5, _fileAnalysis$file_in, _fileAnalysis$parse_r, _fileAnalysis$parse_r2, _fileAnalysis$parse_r3;\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [isImporting, setIsImporting] = useState(false);\n  const [importProgress, setImportProgress] = useState(0);\n  const [showAlert, setShowAlert] = useState(false);\n  const [alertMessage, setAlertMessage] = useState('');\n  const [alertType, setAlertType] = useState('info');\n\n  // Estados para backend\n  const [backendStatus, setBackendStatus] = useState('checking');\n  const [fileAnalysis, setFileAnalysis] = useState(null);\n  const [isProcessingFile, setIsProcessingFile] = useState(false);\n\n  // Nuevos estados para configuración XUI\n  const [contentType, setContentType] = useState('');\n  const [streamsServer, setStreamsServer] = useState('');\n  const [sourceConfig, setSourceConfig] = useState({\n    directSource: true,\n    directProxy: false,\n    loadBalancing: false\n  });\n  const [selectedCategories, setSelectedCategories] = useState([]);\n\n  // Estados para datos dinámicos del backend\n  const [availableServers, setAvailableServers] = useState([]);\n  const [existingCategories, setExistingCategories] = useState([]);\n  const checkBackendStatus = async () => {\n    try {\n      const health = await checkSystemHealth();\n      setBackendStatus(health.success ? 'connected' : 'error');\n      if (!health.success) {\n        displayAlert('warning', 'Backend no disponible. Funcionando en modo offline.');\n      }\n    } catch (error) {\n      setBackendStatus('error');\n      displayAlert('danger', 'No se puede conectar al backend');\n    }\n  };\n  const loadInitialData = async () => {\n    try {\n      console.log(`🔄 Cargando datos iniciales. Backend status: ${backendStatus}`);\n\n      // Cargar servidores y categorías desde backend si está disponible\n      if (backendStatus === 'connected') {\n        console.log('✅ Backend conectado, cargando datos reales...');\n        await loadRealServers();\n        await loadRealCategories();\n      } else {\n        console.log('⚠️ Backend no conectado, usando datos mock...');\n        // Fallback a mock data si no hay conexión\n        loadMockData();\n      }\n    } catch (error) {\n      console.error('Error cargando datos iniciales:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `Error cargando datos iniciales: ${error.message}`);\n      }\n      // Fallback a mock data en caso de error\n      loadMockData();\n    }\n  };\n\n  // Cargar servidores reales desde la base de datos\n  const loadRealServers = async () => {\n    try {\n      console.log('🔄 Iniciando carga de servidores reales...');\n      const response = await fetch('http://localhost:5001/api/database/streaming-servers');\n      console.log('📡 Respuesta del servidor:', response.status, response.statusText);\n      const result = await response.json();\n      console.log('📊 Datos recibidos:', result);\n      if (result.success && result.data) {\n        const servers = result.data.map(server => ({\n          id: server.server_id,\n          name: server.server_name || `Server ${server.server_id}`,\n          ip: server.server_ip || 'Unknown IP',\n          load: `${server.total_streams || 0} streams`,\n          // Mostrar cantidad de streams como \"carga\"\n          total_streams: server.total_streams || 0,\n          status: server.server_status === 1 ? 'Active' : 'Inactive'\n        }));\n        console.log('🖥️ Servidores mapeados:', servers);\n        setAvailableServers(servers);\n        console.log('✅ Estado actualizado con', servers.length, 'servidores');\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargados ${servers.length} servidores reales desde BD`);\n        }\n      } else {\n        console.error('❌ Respuesta no exitosa:', result);\n        throw new Error(result.error || 'No se pudieron cargar servidores');\n      }\n    } catch (error) {\n      console.error('❌ Error cargando servidores:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando servidores: ${error.message}`);\n      }\n      throw error;\n    }\n  };\n\n  // Cargar categorías reales desde la base de datos\n  const loadRealCategories = async () => {\n    try {\n      const result = await databaseAPI.getCategories();\n      if (result.success && result.data) {\n        const categories = result.data.map(cat => ({\n          id: cat.category_id,\n          name: cat.category_name,\n          type: detectCategoryType(cat.category_name),\n          // Detectar tipo basado en nombre\n          parent_id: cat.parent_id\n        }));\n        setExistingCategories(categories);\n        if (window.debugLog) {\n          window.debugLog('success', `✅ Cargadas ${categories.length} categorías reales desde BD`);\n        }\n      } else {\n        throw new Error('No se pudieron cargar categorías');\n      }\n    } catch (error) {\n      console.error('Error cargando categorías:', error);\n      if (window.debugLog) {\n        window.debugLog('error', `❌ Error cargando categorías: ${error.message}`);\n      }\n      throw error;\n    }\n  };\n\n  // Detectar tipo de categoría basado en el nombre\n  const detectCategoryType = categoryName => {\n    const name = categoryName.toLowerCase();\n    if (name.includes('movie') || name.includes('film') || name.includes('cinema')) {\n      return 'movie';\n    } else if (name.includes('series') || name.includes('show') || name.includes('drama')) {\n      return 'series';\n    } else if (name.includes('live') || name.includes('tv') || name.includes('channel') || name.includes('news') || name.includes('sport')) {\n      return 'live';\n    } else if (name.includes('radio') || name.includes('music') || name.includes('fm')) {\n      return 'radio';\n    }\n    return 'live'; // Default a live si no se puede detectar\n  };\n\n  // Datos mock como fallback\n  const loadMockData = () => {\n    setAvailableServers([{\n      id: 1,\n      name: 'Main Server US',\n      ip: '*************',\n      load: '45%'\n    }, {\n      id: 2,\n      name: 'EU Server',\n      ip: '*************',\n      load: '32%'\n    }, {\n      id: 3,\n      name: 'Asia Server',\n      ip: '*************',\n      load: '67%'\n    }, {\n      id: 4,\n      name: 'Backup Server',\n      ip: '*************',\n      load: '12%'\n    }]);\n    setExistingCategories([{\n      id: 1,\n      name: 'Action Movies',\n      type: 'movie'\n    }, {\n      id: 2,\n      name: 'Comedy Movies',\n      type: 'movie'\n    }, {\n      id: 3,\n      name: 'Drama Series',\n      type: 'series'\n    }, {\n      id: 4,\n      name: 'Comedy Series',\n      type: 'series'\n    }, {\n      id: 5,\n      name: 'News Channels',\n      type: 'live'\n    }, {\n      id: 6,\n      name: 'Sports Channels',\n      type: 'live'\n    }, {\n      id: 7,\n      name: 'Entertainment',\n      type: 'live'\n    }, {\n      id: 8,\n      name: 'Music Radio',\n      type: 'radio'\n    }, {\n      id: 9,\n      name: 'Talk Radio',\n      type: 'radio'\n    }]);\n    if (window.debugLog) {\n      window.debugLog('warning', '⚠️ Usando datos mock - backend no disponible');\n    }\n  };\n  const showAlertMessage = (message, type = 'info') => {\n    setAlertMessage(message);\n    setAlertType(type);\n    setShowAlert(true);\n    setTimeout(() => setShowAlert(false), 5000);\n  };\n\n  // Helper function to show alert with better formatting\n  const displayAlert = (type, message) => {\n    showAlertMessage(message, type);\n  };\n\n  // File handling functions\n  const handleBrowseFiles = () => {\n    const fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.m3u,.m3u8';\n    fileInput.onchange = e => {\n      const file = e.target.files[0];\n      if (file) {\n        handleFileSelect({\n          target: {\n            files: [file]\n          }\n        });\n      }\n    };\n    fileInput.click();\n  };\n  const handleAnalyzeFile = async () => {\n    if (!selectedFile) return;\n    setIsProcessingFile(true);\n    setFileAnalysis(null);\n    try {\n      displayAlert('info', 'Analizando archivo M3U...');\n      const response = await api.m3uAPI.analyzeFile(selectedFile);\n      if (response.success) {\n        setFileAnalysis(response.data);\n\n        // Auto-detectar content type basado en el análisis\n        const detectedType = detectContentType(response.data);\n        if (detectedType) {\n          var _response$data$basic_;\n          setContentType(detectedType);\n          displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${((_response$data$basic_ = response.data.basic_analysis) === null || _response$data$basic_ === void 0 ? void 0 : _response$data$basic_.estimated_entries) || 0} entradas. Tipo detectado: ${getContentTypeLabel(detectedType)}`);\n        } else {\n          var _response$data$basic_2;\n          displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${((_response$data$basic_2 = response.data.basic_analysis) === null || _response$data$basic_2 === void 0 ? void 0 : _response$data$basic_2.estimated_entries) || 0} entradas.`);\n        }\n      } else {\n        throw new Error(response.error || 'Error analizando archivo');\n      }\n    } catch (error) {\n      console.error('Error analyzing file:', error);\n      displayAlert('danger', `❌ Error analizando archivo: ${error.message}`);\n    } finally {\n      setIsProcessingFile(false);\n    }\n  };\n  const handleClearAnalysis = () => {\n    setFileAnalysis(null);\n    setSelectedFile(null);\n    setContentType(''); // Limpiar también el content type\n    displayAlert('info', 'Análisis limpiado. Selecciona un nuevo archivo.');\n  };\n\n  // Función para detectar automáticamente el tipo de contenido\n  const detectContentType = analysis => {\n    if (!analysis || !analysis.content_breakdown) return null;\n    const breakdown = analysis.content_breakdown;\n    const total = breakdown.live_tv + breakdown.movies + breakdown.series + breakdown.radio + breakdown.unknown;\n    if (total === 0) return null;\n\n    // Calcular porcentajes\n    const percentages = {\n      live_tv: breakdown.live_tv / total * 100,\n      movies: breakdown.movies / total * 100,\n      series: breakdown.series / total * 100,\n      radio: breakdown.radio / total * 100\n    };\n\n    // Detectar tipo dominante (>60% del contenido)\n    if (percentages.series > 60) return 'series';\n    if (percentages.movies > 60) return 'movie';\n    if (percentages.live_tv > 60) return 'live';\n    if (percentages.radio > 60) return 'radio';\n\n    // Si no hay tipo dominante, elegir el mayor\n    const maxType = Object.keys(percentages).reduce((a, b) => percentages[a] > percentages[b] ? a : b);\n\n    // Mapear nombres\n    const typeMap = {\n      live_tv: 'live',\n      movies: 'movie',\n      series: 'series',\n      radio: 'radio'\n    };\n    return typeMap[maxType] || null;\n  };\n\n  // Función para obtener etiqueta del content type\n  const getContentTypeLabel = type => {\n    const labels = {\n      movie: '🎬 Movies (VOD)',\n      series: '📚 TV Series',\n      live: '📺 Live Channels',\n      radio: '📻 Radio Stations'\n    };\n    return labels[type] || type;\n  };\n\n  // Verificar estado del backend al cargar\n  useEffect(() => {\n    checkBackendStatus();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // Cargar datos cuando el backend status cambie\n  useEffect(() => {\n    if (backendStatus !== 'checking') {\n      loadInitialData();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [backendStatus]);\n  const handleFileSelect = async event => {\n    const file = event.target.files[0];\n    setSelectedFile(file);\n    setFileAnalysis(null);\n    if (file) {\n      displayAlert('info', `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);\n\n      // Solo mostrar información básica, el análisis se hace manualmente\n      if (window.debugLog) {\n        window.debugLog(`📁 File selected: ${file.name}`, 'info');\n        window.debugLog(`📊 File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      }\n    }\n  };\n  const handleImport = async () => {\n    if (!selectedFile || !contentType || !streamsServer) {\n      displayAlert('warning', '⚠️ Por favor completa todos los campos requeridos (archivo, tipo de contenido y servidor).');\n      return;\n    }\n    if (!fileAnalysis) {\n      displayAlert('warning', '⚠️ Por favor analiza el archivo antes de importar.');\n      return;\n    }\n    if (window.debugLog) {\n      window.debugLog(`📥 Starting import of ${selectedFile.name}`, 'info');\n      window.debugLog(`📊 File size: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`, 'info');\n      window.debugLog(`🎯 Content type: ${contentType}`, 'info');\n      window.debugLog(`🖥️ Target server: ${streamsServer}`, 'info');\n    }\n    setIsImporting(true);\n    setImportProgress(0);\n    try {\n      // Preparar configuración de importación\n      const importConfig = {\n        contentType,\n        streamsServer,\n        sourceConfig,\n        categories: selectedCategories,\n        tmdbEnabled: true,\n        autoAssignCategories: true\n      };\n      displayAlert('info', '🔍 Iniciando proceso de importación...');\n      setImportProgress(10);\n\n      // Paso 1: Subir archivo\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('config', JSON.stringify(importConfig));\n      if (window.debugLog) {\n        window.debugLog('📤 Uploading file to backend...', 'info');\n      }\n\n      // Paso 1: Analizar archivo M3U\n      const analyzeResponse = await api.m3uAPI.analyzeFile(selectedFile);\n      setImportProgress(30);\n      if (!analyzeResponse.success) {\n        throw new Error(analyzeResponse.error || 'Error analyzing file');\n      }\n      displayAlert('info', '🎯 Archivo subido, procesando contenido...');\n      // Paso 2: Leer contenido del archivo para parsear episodios\n      const fileContent = await new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = e => resolve(e.target.result);\n        reader.onerror = () => reject(new Error('Error reading file'));\n        reader.readAsText(selectedFile);\n      });\n      setImportProgress(40);\n\n      // Paso 3: Parsear contenido del M3U (series o películas)\n      const parseResponse = await fetch('http://localhost:5001/api/import/parse-content', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          m3uContent: fileContent,\n          contentType: contentType\n        })\n      });\n      const parseResult = await parseResponse.json();\n      if (!parseResult.success) {\n        throw new Error(parseResult.error || 'Error parsing content');\n      }\n      setImportProgress(60);\n\n      // Paso 4: Importar contenido a la base de datos\n      const importPayload = {\n        server_id: parseInt(streamsServer),\n        category_id: selectedCategories.length > 0 ? parseInt(selectedCategories[0]) : null,\n        tmdb_search: sourceConfig.tmdbEnrichment || true,\n        contentType: contentType\n      };\n\n      // Agregar el contenido según el tipo\n      if (contentType === 'series') {\n        importPayload.episodes = parseResult.data.episodes;\n      } else if (contentType === 'movie') {\n        importPayload.movies = parseResult.data.movies;\n      }\n      const importResponse = await fetch('http://localhost:5001/api/import/content', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(importPayload)\n      });\n      const importResult = await importResponse.json();\n      if (!importResult.success) {\n        throw new Error(importResult.error || 'Error importing content');\n      }\n\n      // Paso 5: Finalizar importación\n      setImportProgress(100);\n\n      // Mostrar estadísticas según el tipo de contenido\n      let successMessage = `✅ Importación completada exitosamente!\\n📊 Estadísticas:\\n• ${importResult.imported} elementos importados\\n• ${importResult.errors} errores`;\n      if (contentType === 'series') {\n        successMessage += `\\n• ${importResult.series_created} series creadas\\n• ${importResult.episodes_created} episodios creados`;\n      } else if (contentType === 'movie') {\n        successMessage += `\\n• ${importResult.movies_created} películas creadas`;\n      }\n      displayAlert('success', successMessage);\n      if (window.debugLog) {\n        window.debugLog(`✅ Import completed successfully: ${selectedFile.name}`, 'success');\n        window.debugLog(`📊 Stats: ${JSON.stringify(importResult)}`, 'info');\n      }\n\n      // Limpiar estado después de importación exitosa\n      setTimeout(() => {\n        setSelectedFile(null);\n        setFileAnalysis(null);\n        setContentType('');\n        setStreamsServer('');\n        setSelectedCategories([]);\n      }, 3000);\n    } catch (error) {\n      console.error('Import error:', error);\n      displayAlert('danger', `❌ Error durante la importación: ${error.message}`);\n      if (window.debugLog) {\n        window.debugLog(`❌ Import failed: ${error.message}`, 'error');\n      }\n    } finally {\n      setIsImporting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      maxWidth: 'none'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-primary\",\n        children: \"\\uD83D\\uDCE5 Import M3U Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BackendStatus, {\n        status: backendStatus,\n        onRetry: checkBackendStatus\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 7\n    }, this), showAlert && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: alertType,\n      dismissible: true,\n      onClose: () => setShowAlert(false),\n      children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n        children: [alertType === 'success' && '✅ Success!', alertType === 'danger' && '❌ Error!', alertType === 'warning' && '⚠️ Warning!', alertType === 'info' && 'ℹ️ Information']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-primary text-white d-flex justify-content-between align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDCC2 File Upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), backendStatus === 'connected' && /*#__PURE__*/_jsxDEV(Badge, {\n              bg: \"success\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-cloud-check\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this), \" Backend Ready\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Select M3U File\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"file\",\n                  accept: \".m3u,.m3u8\",\n                  onChange: handleFileSelect,\n                  disabled: isImporting\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"Supported formats: .m3u, .m3u8 (Max size: 50MB)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this), selectedFile && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Selected File:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 23\n                  }, this), \" \", selectedFile.name, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 74\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Size:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 23\n                  }, this), \" \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 21\n                }, this), isProcessingFile && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"secondary\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"spinner-border spinner-border-sm me-2\",\n                      role: \"status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 27\n                    }, this), \"Analizando archivo...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 544,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 23\n                }, this), fileAnalysis && !isProcessingFile && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"success\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    children: \"\\uD83D\\uDCCA An\\xE1lisis del Archivo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Total Lines:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a === void 0 ? void 0 : _fileAnalysis$basic_a.total_lines) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 106\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"EXTINF Entries:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 557,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a2 = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a2 === void 0 ? void 0 : _fileAnalysis$basic_a2.extinf_lines) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 557,\n                        columnNumber: 110\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"URL Entries:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 558,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a3 = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a3 === void 0 ? void 0 : _fileAnalysis$basic_a3.url_lines) || 0]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 555,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Estimated Entries:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 561,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$basic_a4 = fileAnalysis.basic_analysis) === null || _fileAnalysis$basic_a4 === void 0 ? void 0 : _fileAnalysis$basic_a4.estimated_entries) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 561,\n                        columnNumber: 118\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Valid M3U:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 562,\n                        columnNumber: 29\n                      }, this), \" \", (_fileAnalysis$basic_a5 = fileAnalysis.basic_analysis) !== null && _fileAnalysis$basic_a5 !== void 0 && _fileAnalysis$basic_a5.has_valid_m3u_header ? '✅ Yes' : '❌ No', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 562,\n                        columnNumber: 127\n                      }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"File Size:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 563,\n                        columnNumber: 29\n                      }, this), \" \", ((_fileAnalysis$file_in = fileAnalysis.file_info) === null || _fileAnalysis$file_in === void 0 ? void 0 : _fileAnalysis$file_in.size_mb) || 0, \" MB\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 25\n                  }, this), ((_fileAnalysis$parse_r = fileAnalysis.parse_results) === null || _fileAnalysis$parse_r === void 0 ? void 0 : (_fileAnalysis$parse_r2 = _fileAnalysis$parse_r.series) === null || _fileAnalysis$parse_r2 === void 0 ? void 0 : _fileAnalysis$parse_r2.success) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Series Detected:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 569,\n                      columnNumber: 29\n                    }, this), \" \", ((_fileAnalysis$parse_r3 = fileAnalysis.parse_results.series.data) === null || _fileAnalysis$parse_r3 === void 0 ? void 0 : _fileAnalysis$parse_r3.length) || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleBrowseFiles,\n                  variant: \"primary\",\n                  disabled: isProcessingFile,\n                  children: \"Seleccionar Archivo M3U\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 19\n                }, this), selectedFile && !fileAnalysis && /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleAnalyzeFile,\n                  variant: \"info\",\n                  className: \"ms-2\",\n                  disabled: isProcessingFile,\n                  children: isProcessingFile ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"spinner-border spinner-border-sm me-2\",\n                      role: \"status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 27\n                    }, this), \"Analizando...\"]\n                  }, void 0, true) : 'Analizar Archivo'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 21\n                }, this), fileAnalysis && /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleClearAnalysis,\n                  variant: \"outline-secondary\",\n                  className: \"ms-2\",\n                  disabled: isProcessingFile,\n                  children: \"Limpiar An\\xE1lisis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\uD83C\\uDFAF Content Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: contentType,\n                  onChange: e => setContentType(e.target.value),\n                  disabled: isImporting,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select content type...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"movie\",\n                    children: \"\\uD83C\\uDFAC Movies (VOD)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"series\",\n                    children: \"\\uD83D\\uDCDA TV Series\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"live\",\n                    children: \"\\uD83D\\uDCFA Live Channels\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"radio\",\n                    children: \"\\uD83D\\uDCFB Radio Stations\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"This determines how content will be categorized in XUI streams table\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this), contentType && /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"mb-0\",\n                    children: \"\\uD83D\\uDDA5\\uFE0F Target Streams Server\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-secondary\",\n                    size: \"sm\",\n                    onClick: loadRealServers,\n                    disabled: isImporting || backendStatus !== 'connected',\n                    children: \"\\uD83D\\uDD04 Refresh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: streamsServer,\n                  onChange: e => setStreamsServer(e.target.value),\n                  disabled: isImporting,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select streams server...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 23\n                  }, this), availableServers.map(server => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: server.id,\n                    children: [server.name, \" (\", server.ip, \") - \", server.load]\n                  }, server.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: [\"Server where streams will be hosted and served from.\", availableServers.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success\",\n                    children: [\" \\u2705 \", availableServers.length, \" servers loaded\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 662,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this), streamsServer && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"\\uD83D\\uDD17 Source Configuration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    label: \"\\u2705 Direct Source (recommended for better performance)\",\n                    checked: sourceConfig.directSource,\n                    onChange: e => setSourceConfig(prev => ({\n                      ...prev,\n                      directSource: e.target.checked\n                    })),\n                    disabled: isImporting\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    label: \"\\uD83D\\uDD04 Direct Proxy (for geo-restricted content)\",\n                    checked: sourceConfig.directProxy,\n                    onChange: e => setSourceConfig(prev => ({\n                      ...prev,\n                      directProxy: e.target.checked\n                    })),\n                    disabled: isImporting\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    label: \"\\u2696\\uFE0F Load Balancing (distribute across servers)\",\n                    checked: sourceConfig.loadBalancing,\n                    onChange: e => setSourceConfig(prev => ({\n                      ...prev,\n                      loadBalancing: e.target.checked\n                    })),\n                    disabled: isImporting\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"\\uD83C\\uDFF7\\uFE0F Categories Assignment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      maxHeight: '120px',\n                      overflowY: 'auto',\n                      border: '1px solid #ddd',\n                      padding: '8px',\n                      borderRadius: '4px'\n                    },\n                    children: existingCategories.filter(cat => !contentType || cat.type === contentType || contentType === 'live').map(category => /*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"checkbox\",\n                      label: `${category.name} (${category.type})`,\n                      checked: selectedCategories.includes(category.id),\n                      onChange: e => {\n                        if (e.target.checked) {\n                          setSelectedCategories(prev => [...prev, category.id]);\n                        } else {\n                          setSelectedCategories(prev => prev.filter(id => id !== category.id));\n                        }\n                      },\n                      disabled: isImporting\n                    }, category.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                    className: \"text-muted\",\n                    children: \"Select existing categories or new ones will be created automatically\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\u2699\\uFE0F Import Settings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  label: \"\\uD83D\\uDD04 Auto-rename with TMDB data\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  label: \"\\uD83D\\uDCC2 Auto-assign categories\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  label: \"\\uD83C\\uDFAC Process VOD metadata\",\n                  defaultChecked: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 17\n              }, this), isImporting && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Import Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n                  now: importProgress,\n                  label: `${importProgress}%`,\n                  variant: importProgress === 100 ? 'success' : 'primary',\n                  animated: importProgress < 100\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 19\n              }, this), selectedFile && contentType === 'series' && /*#__PURE__*/_jsxDEV(SeriesImportHandler, {\n                selectedFile: selectedFile,\n                isImporting: isImporting,\n                onSeriesDetected: series => {\n                  window.debugLog('info', `📺 Detectadas ${series.length} series para importar`);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                size: \"lg\",\n                onClick: handleImport,\n                disabled: !selectedFile || !fileAnalysis || !contentType || !streamsServer || isImporting || isProcessingFile,\n                className: \"w-100\",\n                children: isImporting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"spinner-border spinner-border-sm me-2\",\n                    role: \"status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 23\n                  }, this), \"Importando... \", importProgress, \"%\"]\n                }, void 0, true) : !selectedFile ? '📁 Selecciona un archivo M3U' : !fileAnalysis ? '� Analiza el archivo primero' : !contentType || !streamsServer ? '⚙️ Completa la configuración' : '🚀 Iniciar Importación'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\u2139\\uFE0F Import Guidelines\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\uD83D\\uDCCB Supported Content Types:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCFA Live Channels:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 21\n                }, this), \" TV channels with EPG support\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAC VOD (Movies):\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 21\n                }, this), \" On-demand movie content\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCDA Series:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 21\n                }, this), \" TV series with episode management\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFB5 Radio:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 816,\n                  columnNumber: 21\n                }, this), \" Audio streaming channels\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\u2699\\uFE0F Processing Features:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFAF TMDB Integration:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 21\n                }, this), \" Auto-fetch metadata\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83C\\uDFF7\\uFE0F Category Assignment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 21\n                }, this), \" Smart categorization\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDDBC\\uFE0F Poster Download:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 21\n                }, this), \" High-quality artwork\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCDD Description Parsing:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 21\n                }, this), \" Extract show info\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"\\u26A1 Performance Tips:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Files under 10MB import faster\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Use UTF-8 encoding for special characters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Clean duplicate entries before import\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Ensure stable internet for metadata fetching\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-secondary text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"\\uD83D\\uDCCA Recent Import Queue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              striped: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCC4 File\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCC5 Queued\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83D\\uDCCA Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 851,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\uD83C\\uDFAF Target\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 852,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u26A1 Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 853,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"premium_list.m3u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"2025-07-15 15:30\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 859,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-warning\",\n                      children: \"\\u23F3 Queued\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 860,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"Main Server\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-primary\",\n                      className: \"me-1\",\n                      children: \"\\u25B6\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 863,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-danger\",\n                      children: \"\\u274C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 864,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 862,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 857,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"sports_channels.m3u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 868,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"2025-07-15 15:25\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge bg-success\",\n                      children: \"\\u2705 Processing\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 870,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"Cloud Server\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"sm\",\n                      variant: \"outline-info\",\n                      children: \"\\uD83D\\uDCCA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 867,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 841,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 840,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 839,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 488,\n    columnNumber: 5\n  }, this);\n};\n_s(ImportM3U, \"aTGxZhgsjNS2R2ob6ACBs4acQmM=\");\n_c = ImportM3U;\nexport default ImportM3U;\nvar _c;\n$RefreshReg$(_c, \"ImportM3U\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "ProgressBar", "Table", "Badge", "SeriesImportHandler", "BackendStatus", "checkSystemHealth", "api", "databaseAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImportM3U", "_s", "_fileAnalysis$basic_a", "_fileAnalysis$basic_a2", "_fileAnalysis$basic_a3", "_fileAnalysis$basic_a4", "_fileAnalysis$basic_a5", "_fileAnalysis$file_in", "_fileAnalysis$parse_r", "_fileAnalysis$parse_r2", "_fileAnalysis$parse_r3", "selectedFile", "setSelectedFile", "isImporting", "setIsImporting", "importProgress", "setImportProgress", "show<PERSON><PERSON><PERSON>", "setShowAlert", "alertMessage", "setAlertMessage", "alertType", "setAlertType", "backendStatus", "setBackendStatus", "fileAnalysis", "setFileAnalysis", "isProcessingFile", "setIsProcessingFile", "contentType", "setContentType", "streamsServer", "setStreamsServer", "sourceConfig", "setSourceConfig", "directSource", "directProxy", "loadBalancing", "selectedCategories", "setSelectedCategories", "availableServers", "setAvailableServers", "existingCategories", "setExistingCategories", "checkBackendStatus", "health", "success", "displayAlert", "error", "loadInitialData", "console", "log", "loadRealServers", "loadRealCategories", "loadMockData", "window", "debugLog", "message", "response", "fetch", "status", "statusText", "result", "json", "data", "servers", "map", "server", "id", "server_id", "name", "server_name", "ip", "server_ip", "load", "total_streams", "server_status", "length", "Error", "getCategories", "categories", "cat", "category_id", "category_name", "type", "detectCategoryType", "parent_id", "categoryName", "toLowerCase", "includes", "showAlertMessage", "setTimeout", "handleBrowseFiles", "fileInput", "document", "createElement", "accept", "onchange", "e", "file", "target", "files", "handleFileSelect", "click", "handleAnalyzeFile", "m3uAPI", "analyzeFile", "detectedType", "detectContentType", "_response$data$basic_", "basic_analysis", "estimated_entries", "getContentTypeLabel", "_response$data$basic_2", "handleClearAnalysis", "analysis", "content_breakdown", "breakdown", "total", "live_tv", "movies", "series", "radio", "unknown", "percentages", "maxType", "Object", "keys", "reduce", "a", "b", "typeMap", "labels", "movie", "live", "event", "size", "toFixed", "handleImport", "importConfig", "tmdbEnabled", "autoAssignCategories", "formData", "FormData", "append", "JSON", "stringify", "analyzeResponse", "fileContent", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "onerror", "readAsText", "parseResponse", "method", "headers", "body", "m3uContent", "parseResult", "importPayload", "parseInt", "tmdb_search", "tmdbEnrichment", "episodes", "importResponse", "importResult", "successMessage", "imported", "errors", "series_created", "episodes_created", "movies_created", "style", "width", "max<PERSON><PERSON><PERSON>", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onRetry", "variant", "dismissible", "onClose", "Heading", "lg", "Header", "bg", "Body", "Group", "Label", "Control", "onChange", "disabled", "Text", "role", "md", "total_lines", "extinf_lines", "url_lines", "has_valid_m3u_header", "file_info", "size_mb", "parse_results", "onClick", "Select", "value", "Check", "label", "checked", "prev", "maxHeight", "overflowY", "border", "padding", "borderRadius", "filter", "category", "defaultChecked", "now", "animated", "onSeriesDetected", "striped", "hover", "_c", "$RefreshReg$"], "sources": ["F:/WORKSPACE/XUI IMPORTER/xui-importer/src/components/ImportM3U.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Row, Col, Card, Button, Form, Alert, ProgressBar, Table, Badge } from 'react-bootstrap';\r\nimport SeriesImportHandler from './SeriesImportHandler';\r\nimport BackendStatus from './BackendStatus';\r\n\r\nimport {\r\n  checkSystemHealth\r\n} from '../utils/seriesLogic';\r\nimport { api, databaseAPI } from '../services/apiService';\r\n\r\nconst ImportM3U = () => {\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [isImporting, setIsImporting] = useState(false);\r\n  const [importProgress, setImportProgress] = useState(0);\r\n  const [showAlert, setShowAlert] = useState(false);\r\n  const [alertMessage, setAlertMessage] = useState('');\r\n  const [alertType, setAlertType] = useState('info');\r\n  \r\n  // Estados para backend\r\n  const [backendStatus, setBackendStatus] = useState('checking');\r\n  const [fileAnalysis, setFileAnalysis] = useState(null);\r\n  const [isProcessingFile, setIsProcessingFile] = useState(false);\r\n  \r\n  // Nuevos estados para configuración XUI\r\n  const [contentType, setContentType] = useState('');\r\n  const [streamsServer, setStreamsServer] = useState('');\r\n  const [sourceConfig, setSourceConfig] = useState({\r\n    directSource: true,\r\n    directProxy: false,\r\n    loadBalancing: false\r\n  });\r\n  const [selectedCategories, setSelectedCategories] = useState([]);\r\n\r\n\r\n\r\n  // Estados para datos dinámicos del backend\r\n  const [availableServers, setAvailableServers] = useState([]);\r\n  const [existingCategories, setExistingCategories] = useState([]);\r\n\r\n  const checkBackendStatus = async () => {\r\n    try {\r\n      const health = await checkSystemHealth();\r\n      setBackendStatus(health.success ? 'connected' : 'error');\r\n      \r\n      if (!health.success) {\r\n        displayAlert('warning', 'Backend no disponible. Funcionando en modo offline.');\r\n      }\r\n    } catch (error) {\r\n      setBackendStatus('error');\r\n      displayAlert('danger', 'No se puede conectar al backend');\r\n    }\r\n  };\r\n\r\n  const loadInitialData = async () => {\r\n    try {\r\n      console.log(`🔄 Cargando datos iniciales. Backend status: ${backendStatus}`);\r\n\r\n      // Cargar servidores y categorías desde backend si está disponible\r\n      if (backendStatus === 'connected') {\r\n        console.log('✅ Backend conectado, cargando datos reales...');\r\n        await loadRealServers();\r\n        await loadRealCategories();\r\n      } else {\r\n        console.log('⚠️ Backend no conectado, usando datos mock...');\r\n        // Fallback a mock data si no hay conexión\r\n        loadMockData();\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('Error cargando datos iniciales:', error);\r\n      if (window.debugLog) {\r\n        window.debugLog('error', `Error cargando datos iniciales: ${error.message}`);\r\n      }\r\n      // Fallback a mock data en caso de error\r\n      loadMockData();\r\n    }\r\n  };\r\n\r\n  // Cargar servidores reales desde la base de datos\r\n  const loadRealServers = async () => {\r\n    try {\r\n      console.log('🔄 Iniciando carga de servidores reales...');\r\n\r\n      const response = await fetch('http://localhost:5001/api/database/streaming-servers');\r\n      console.log('📡 Respuesta del servidor:', response.status, response.statusText);\r\n\r\n      const result = await response.json();\r\n      console.log('📊 Datos recibidos:', result);\r\n\r\n      if (result.success && result.data) {\r\n        const servers = result.data.map(server => ({\r\n          id: server.server_id,\r\n          name: server.server_name || `Server ${server.server_id}`,\r\n          ip: server.server_ip || 'Unknown IP',\r\n          load: `${server.total_streams || 0} streams`, // Mostrar cantidad de streams como \"carga\"\r\n          total_streams: server.total_streams || 0,\r\n          status: server.server_status === 1 ? 'Active' : 'Inactive'\r\n        }));\r\n\r\n        console.log('🖥️ Servidores mapeados:', servers);\r\n        setAvailableServers(servers);\r\n        console.log('✅ Estado actualizado con', servers.length, 'servidores');\r\n\r\n        if (window.debugLog) {\r\n          window.debugLog('success', `✅ Cargados ${servers.length} servidores reales desde BD`);\r\n        }\r\n      } else {\r\n        console.error('❌ Respuesta no exitosa:', result);\r\n        throw new Error(result.error || 'No se pudieron cargar servidores');\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error cargando servidores:', error);\r\n      if (window.debugLog) {\r\n        window.debugLog('error', `❌ Error cargando servidores: ${error.message}`);\r\n      }\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Cargar categorías reales desde la base de datos\r\n  const loadRealCategories = async () => {\r\n    try {\r\n      const result = await databaseAPI.getCategories();\r\n\r\n      if (result.success && result.data) {\r\n        const categories = result.data.map(cat => ({\r\n          id: cat.category_id,\r\n          name: cat.category_name,\r\n          type: detectCategoryType(cat.category_name), // Detectar tipo basado en nombre\r\n          parent_id: cat.parent_id\r\n        }));\r\n\r\n        setExistingCategories(categories);\r\n\r\n        if (window.debugLog) {\r\n          window.debugLog('success', `✅ Cargadas ${categories.length} categorías reales desde BD`);\r\n        }\r\n      } else {\r\n        throw new Error('No se pudieron cargar categorías');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error cargando categorías:', error);\r\n      if (window.debugLog) {\r\n        window.debugLog('error', `❌ Error cargando categorías: ${error.message}`);\r\n      }\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Detectar tipo de categoría basado en el nombre\r\n  const detectCategoryType = (categoryName) => {\r\n    const name = categoryName.toLowerCase();\r\n\r\n    if (name.includes('movie') || name.includes('film') || name.includes('cinema')) {\r\n      return 'movie';\r\n    } else if (name.includes('series') || name.includes('show') || name.includes('drama')) {\r\n      return 'series';\r\n    } else if (name.includes('live') || name.includes('tv') || name.includes('channel') || name.includes('news') || name.includes('sport')) {\r\n      return 'live';\r\n    } else if (name.includes('radio') || name.includes('music') || name.includes('fm')) {\r\n      return 'radio';\r\n    }\r\n\r\n    return 'live'; // Default a live si no se puede detectar\r\n  };\r\n\r\n  // Datos mock como fallback\r\n  const loadMockData = () => {\r\n    setAvailableServers([\r\n      { id: 1, name: 'Main Server US', ip: '*************', load: '45%' },\r\n      { id: 2, name: 'EU Server', ip: '*************', load: '32%' },\r\n      { id: 3, name: 'Asia Server', ip: '*************', load: '67%' },\r\n      { id: 4, name: 'Backup Server', ip: '*************', load: '12%' }\r\n    ]);\r\n\r\n    setExistingCategories([\r\n      { id: 1, name: 'Action Movies', type: 'movie' },\r\n      { id: 2, name: 'Comedy Movies', type: 'movie' },\r\n      { id: 3, name: 'Drama Series', type: 'series' },\r\n      { id: 4, name: 'Comedy Series', type: 'series' },\r\n      { id: 5, name: 'News Channels', type: 'live' },\r\n      { id: 6, name: 'Sports Channels', type: 'live' },\r\n      { id: 7, name: 'Entertainment', type: 'live' },\r\n      { id: 8, name: 'Music Radio', type: 'radio' },\r\n      { id: 9, name: 'Talk Radio', type: 'radio' }\r\n    ]);\r\n\r\n    if (window.debugLog) {\r\n      window.debugLog('warning', '⚠️ Usando datos mock - backend no disponible');\r\n    }\r\n  };\r\n\r\n  const showAlertMessage = (message, type = 'info') => {\r\n    setAlertMessage(message);\r\n    setAlertType(type);\r\n    setShowAlert(true);\r\n    setTimeout(() => setShowAlert(false), 5000);\r\n  };\r\n\r\n  // Helper function to show alert with better formatting\r\n  const displayAlert = (type, message) => {\r\n    showAlertMessage(message, type);\r\n  };\r\n\r\n  // File handling functions\r\n  const handleBrowseFiles = () => {\r\n    const fileInput = document.createElement('input');\r\n    fileInput.type = 'file';\r\n    fileInput.accept = '.m3u,.m3u8';\r\n    fileInput.onchange = (e) => {\r\n      const file = e.target.files[0];\r\n      if (file) {\r\n        handleFileSelect({ target: { files: [file] } });\r\n      }\r\n    };\r\n    fileInput.click();\r\n  };\r\n\r\n  const handleAnalyzeFile = async () => {\r\n    if (!selectedFile) return;\r\n    \r\n    setIsProcessingFile(true);\r\n    setFileAnalysis(null);\r\n    \r\n    try {\r\n      displayAlert('info', 'Analizando archivo M3U...');\r\n      \r\n      const response = await api.m3uAPI.analyzeFile(selectedFile);\r\n      \r\n      if (response.success) {\r\n        setFileAnalysis(response.data);\r\n\r\n        // Auto-detectar content type basado en el análisis\r\n        const detectedType = detectContentType(response.data);\r\n        if (detectedType) {\r\n          setContentType(detectedType);\r\n          displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${response.data.basic_analysis?.estimated_entries || 0} entradas. Tipo detectado: ${getContentTypeLabel(detectedType)}`);\r\n        } else {\r\n          displayAlert('success', `✅ Archivo analizado correctamente. Se detectaron ${response.data.basic_analysis?.estimated_entries || 0} entradas.`);\r\n        }\r\n      } else {\r\n        throw new Error(response.error || 'Error analizando archivo');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error analyzing file:', error);\r\n      displayAlert('danger', `❌ Error analizando archivo: ${error.message}`);\r\n    } finally {\r\n      setIsProcessingFile(false);\r\n    }\r\n  };\r\n\r\n  const handleClearAnalysis = () => {\r\n    setFileAnalysis(null);\r\n    setSelectedFile(null);\r\n    setContentType(''); // Limpiar también el content type\r\n    displayAlert('info', 'Análisis limpiado. Selecciona un nuevo archivo.');\r\n  };\r\n\r\n  // Función para detectar automáticamente el tipo de contenido\r\n  const detectContentType = (analysis) => {\r\n    if (!analysis || !analysis.content_breakdown) return null;\r\n\r\n    const breakdown = analysis.content_breakdown;\r\n    const total = breakdown.live_tv + breakdown.movies + breakdown.series + breakdown.radio + breakdown.unknown;\r\n\r\n    if (total === 0) return null;\r\n\r\n    // Calcular porcentajes\r\n    const percentages = {\r\n      live_tv: (breakdown.live_tv / total) * 100,\r\n      movies: (breakdown.movies / total) * 100,\r\n      series: (breakdown.series / total) * 100,\r\n      radio: (breakdown.radio / total) * 100\r\n    };\r\n\r\n    // Detectar tipo dominante (>60% del contenido)\r\n    if (percentages.series > 60) return 'series';\r\n    if (percentages.movies > 60) return 'movie';\r\n    if (percentages.live_tv > 60) return 'live';\r\n    if (percentages.radio > 60) return 'radio';\r\n\r\n    // Si no hay tipo dominante, elegir el mayor\r\n    const maxType = Object.keys(percentages).reduce((a, b) =>\r\n      percentages[a] > percentages[b] ? a : b\r\n    );\r\n\r\n    // Mapear nombres\r\n    const typeMap = {\r\n      live_tv: 'live',\r\n      movies: 'movie',\r\n      series: 'series',\r\n      radio: 'radio'\r\n    };\r\n\r\n    return typeMap[maxType] || null;\r\n  };\r\n\r\n  // Función para obtener etiqueta del content type\r\n  const getContentTypeLabel = (type) => {\r\n    const labels = {\r\n      movie: '🎬 Movies (VOD)',\r\n      series: '📚 TV Series',\r\n      live: '📺 Live Channels',\r\n      radio: '📻 Radio Stations'\r\n    };\r\n    return labels[type] || type;\r\n  };\r\n\r\n  // Verificar estado del backend al cargar\r\n  useEffect(() => {\r\n    checkBackendStatus();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  // Cargar datos cuando el backend status cambie\r\n  useEffect(() => {\r\n    if (backendStatus !== 'checking') {\r\n      loadInitialData();\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [backendStatus]);\r\n\r\n  const handleFileSelect = async (event) => {\r\n    const file = event.target.files[0];\r\n    setSelectedFile(file);\r\n    setFileAnalysis(null);\r\n    \r\n    if (file) {\r\n      displayAlert('info', `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);\r\n      \r\n      // Solo mostrar información básica, el análisis se hace manualmente\r\n      if (window.debugLog) {\r\n        window.debugLog(`📁 File selected: ${file.name}`, 'info');\r\n        window.debugLog(`📊 File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 'info');\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImport = async () => {\r\n    if (!selectedFile || !contentType || !streamsServer) {\r\n      displayAlert('warning', '⚠️ Por favor completa todos los campos requeridos (archivo, tipo de contenido y servidor).');\r\n      return;\r\n    }\r\n\r\n\r\n    \r\n    if (!fileAnalysis) {\r\n      displayAlert('warning', '⚠️ Por favor analiza el archivo antes de importar.');\r\n      return;\r\n    }\r\n    \r\n    if (window.debugLog) {\r\n      window.debugLog(`📥 Starting import of ${selectedFile.name}`, 'info');\r\n      window.debugLog(`📊 File size: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`, 'info');\r\n      window.debugLog(`🎯 Content type: ${contentType}`, 'info');\r\n      window.debugLog(`🖥️ Target server: ${streamsServer}`, 'info');\r\n    }\r\n    \r\n    setIsImporting(true);\r\n    setImportProgress(0);\r\n    \r\n    try {\r\n      // Preparar configuración de importación\r\n      const importConfig = {\r\n        contentType,\r\n        streamsServer,\r\n        sourceConfig,\r\n        categories: selectedCategories,\r\n        tmdbEnabled: true,\r\n        autoAssignCategories: true\r\n      };\r\n      \r\n      displayAlert('info', '🔍 Iniciando proceso de importación...');\r\n      setImportProgress(10);\r\n      \r\n      // Paso 1: Subir archivo\r\n      const formData = new FormData();\r\n      formData.append('file', selectedFile);\r\n      formData.append('config', JSON.stringify(importConfig));\r\n      \r\n      if (window.debugLog) {\r\n        window.debugLog('📤 Uploading file to backend...', 'info');\r\n      }\r\n      \r\n      // Paso 1: Analizar archivo M3U\r\n      const analyzeResponse = await api.m3uAPI.analyzeFile(selectedFile);\r\n      setImportProgress(30);\r\n\r\n      if (!analyzeResponse.success) {\r\n        throw new Error(analyzeResponse.error || 'Error analyzing file');\r\n      }\r\n\r\n      displayAlert('info', '🎯 Archivo subido, procesando contenido...');\r\n      // Paso 2: Leer contenido del archivo para parsear episodios\r\n      const fileContent = await new Promise((resolve, reject) => {\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => resolve(e.target.result);\r\n        reader.onerror = () => reject(new Error('Error reading file'));\r\n        reader.readAsText(selectedFile);\r\n      });\r\n\r\n      setImportProgress(40);\r\n\r\n      // Paso 3: Parsear contenido del M3U (series o películas)\r\n      const parseResponse = await fetch('http://localhost:5001/api/import/parse-content', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          m3uContent: fileContent,\r\n          contentType: contentType\r\n        })\r\n      });\r\n\r\n      const parseResult = await parseResponse.json();\r\n      if (!parseResult.success) {\r\n        throw new Error(parseResult.error || 'Error parsing content');\r\n      }\r\n\r\n      setImportProgress(60);\r\n\r\n      // Paso 4: Importar contenido a la base de datos\r\n      const importPayload = {\r\n        server_id: parseInt(streamsServer),\r\n        category_id: selectedCategories.length > 0 ? parseInt(selectedCategories[0]) : null,\r\n        tmdb_search: sourceConfig.tmdbEnrichment || true,\r\n        contentType: contentType\r\n      };\r\n\r\n      // Agregar el contenido según el tipo\r\n      if (contentType === 'series') {\r\n        importPayload.episodes = parseResult.data.episodes;\r\n      } else if (contentType === 'movie') {\r\n        importPayload.movies = parseResult.data.movies;\r\n      }\r\n\r\n      const importResponse = await fetch('http://localhost:5001/api/import/content', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(importPayload)\r\n      });\r\n\r\n      const importResult = await importResponse.json();\r\n      if (!importResult.success) {\r\n        throw new Error(importResult.error || 'Error importing content');\r\n      }\r\n\r\n      // Paso 5: Finalizar importación\r\n      setImportProgress(100);\r\n\r\n      // Mostrar estadísticas según el tipo de contenido\r\n      let successMessage = `✅ Importación completada exitosamente!\\n📊 Estadísticas:\\n• ${importResult.imported} elementos importados\\n• ${importResult.errors} errores`;\r\n\r\n      if (contentType === 'series') {\r\n        successMessage += `\\n• ${importResult.series_created} series creadas\\n• ${importResult.episodes_created} episodios creados`;\r\n      } else if (contentType === 'movie') {\r\n        successMessage += `\\n• ${importResult.movies_created} películas creadas`;\r\n      }\r\n\r\n      displayAlert('success', successMessage);\r\n\r\n      if (window.debugLog) {\r\n        window.debugLog(`✅ Import completed successfully: ${selectedFile.name}`, 'success');\r\n        window.debugLog(`📊 Stats: ${JSON.stringify(importResult)}`, 'info');\r\n      }\r\n\r\n      // Limpiar estado después de importación exitosa\r\n      setTimeout(() => {\r\n        setSelectedFile(null);\r\n        setFileAnalysis(null);\r\n        setContentType('');\r\n        setStreamsServer('');\r\n        setSelectedCategories([]);\r\n      }, 3000);\r\n      \r\n    } catch (error) {\r\n      console.error('Import error:', error);\r\n      displayAlert('danger', `❌ Error durante la importación: ${error.message}`);\r\n      \r\n      if (window.debugLog) {\r\n        window.debugLog(`❌ Import failed: ${error.message}`, 'error');\r\n      }\r\n    } finally {\r\n      setIsImporting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={{ width: '100%', maxWidth: 'none' }}>\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h1 className=\"text-primary\">📥 Import M3U Files</h1>\r\n        <BackendStatus \r\n          status={backendStatus} \r\n          onRetry={checkBackendStatus}\r\n        />\r\n      </div>\r\n\r\n      {showAlert && (\r\n        <Alert variant={alertType} dismissible onClose={() => setShowAlert(false)}>\r\n          <Alert.Heading>\r\n            {alertType === 'success' && '✅ Success!'}\r\n            {alertType === 'danger' && '❌ Error!'}\r\n            {alertType === 'warning' && '⚠️ Warning!'}\r\n            {alertType === 'info' && 'ℹ️ Information'}\r\n          </Alert.Heading>\r\n          <p>{alertMessage}</p>\r\n        </Alert>\r\n      )}\r\n\r\n      <Row className=\"mb-4\">\r\n        <Col lg={6}>\r\n          <Card className=\"shadow-sm h-100\">\r\n            <Card.Header className=\"bg-primary text-white d-flex justify-content-between align-items-center\">\r\n              <h5 className=\"mb-0\">📂 File Upload</h5>\r\n              {backendStatus === 'connected' && (\r\n                <Badge bg=\"success\">\r\n                  <i className=\"bi bi-cloud-check\"></i> Backend Ready\r\n                </Badge>\r\n              )}\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <Form>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Select M3U File</Form.Label>\r\n                  <Form.Control \r\n                    type=\"file\" \r\n                    accept=\".m3u,.m3u8\"\r\n                    onChange={handleFileSelect}\r\n                    disabled={isImporting}\r\n                  />\r\n                  <Form.Text className=\"text-muted\">\r\n                    Supported formats: .m3u, .m3u8 (Max size: 50MB)\r\n                  </Form.Text>\r\n                </Form.Group>\r\n\r\n                {selectedFile && (\r\n                  <>\r\n                    <Alert variant=\"info\">\r\n                      <strong>Selected File:</strong> {selectedFile.name}<br/>\r\n                      <strong>Size:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\r\n                    </Alert>\r\n                    \r\n                    {isProcessingFile && (\r\n                      <Alert variant=\"secondary\">\r\n                        <div className=\"d-flex align-items-center\">\r\n                          <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\"></div>\r\n                          Analizando archivo...\r\n                        </div>\r\n                      </Alert>\r\n                    )}\r\n                    \r\n                    {fileAnalysis && !isProcessingFile && (\r\n                      <Alert variant=\"success\">\r\n                        <h6>📊 Análisis del Archivo</h6>\r\n                        <Row>\r\n                          <Col md={6}>\r\n                            <strong>Total Lines:</strong> {fileAnalysis.basic_analysis?.total_lines || 0}<br/>\r\n                            <strong>EXTINF Entries:</strong> {fileAnalysis.basic_analysis?.extinf_lines || 0}<br/>\r\n                            <strong>URL Entries:</strong> {fileAnalysis.basic_analysis?.url_lines || 0}\r\n                          </Col>\r\n                          <Col md={6}>\r\n                            <strong>Estimated Entries:</strong> {fileAnalysis.basic_analysis?.estimated_entries || 0}<br/>\r\n                            <strong>Valid M3U:</strong> {fileAnalysis.basic_analysis?.has_valid_m3u_header ? '✅ Yes' : '❌ No'}<br/>\r\n                            <strong>File Size:</strong> {fileAnalysis.file_info?.size_mb || 0} MB\r\n                          </Col>\r\n                        </Row>\r\n                        \r\n                        {fileAnalysis.parse_results?.series?.success && (\r\n                          <div className=\"mt-2\">\r\n                            <strong>Series Detected:</strong> {fileAnalysis.parse_results.series.data?.length || 0}\r\n                          </div>\r\n                        )}\r\n                      </Alert>\r\n                    )}\r\n                  </>\r\n                )}\r\n\r\n                <div className=\"mb-3\">\r\n                  <Button \r\n                    onClick={handleBrowseFiles} \r\n                    variant=\"primary\"\r\n                    disabled={isProcessingFile}\r\n                  >\r\n                    Seleccionar Archivo M3U\r\n                  </Button>\r\n                  \r\n                  {selectedFile && !fileAnalysis && (\r\n                    <Button \r\n                      onClick={handleAnalyzeFile} \r\n                      variant=\"info\" \r\n                      className=\"ms-2\"\r\n                      disabled={isProcessingFile}\r\n                    >\r\n                      {isProcessingFile ? (\r\n                        <>\r\n                          <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n                          Analizando...\r\n                        </>\r\n                      ) : (\r\n                        'Analizar Archivo'\r\n                      )}\r\n                    </Button>\r\n                  )}\r\n                  \r\n                  {fileAnalysis && (\r\n                    <Button \r\n                      onClick={handleClearAnalysis} \r\n                      variant=\"outline-secondary\" \r\n                      className=\"ms-2\"\r\n                      disabled={isProcessingFile}\r\n                    >\r\n                      Limpiar Análisis\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>🎯 Content Type</Form.Label>\r\n                  <Form.Select\r\n                    value={contentType}\r\n                    onChange={(e) => setContentType(e.target.value)}\r\n                    disabled={isImporting}\r\n                  >\r\n                    <option value=\"\">Select content type...</option>\r\n                    <option value=\"movie\">🎬 Movies (VOD)</option>\r\n                    <option value=\"series\">📚 TV Series</option>\r\n                    <option value=\"live\">📺 Live Channels</option>\r\n                    <option value=\"radio\">📻 Radio Stations</option>\r\n                  </Form.Select>\r\n                  <Form.Text className=\"text-muted\">\r\n                    This determines how content will be categorized in XUI streams table\r\n                  </Form.Text>\r\n                </Form.Group>\r\n\r\n                {contentType && (\r\n                  <Form.Group className=\"mb-3\">\r\n                    <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                      <Form.Label className=\"mb-0\">🖥️ Target Streams Server</Form.Label>\r\n                      <Button\r\n                        variant=\"outline-secondary\"\r\n                        size=\"sm\"\r\n                        onClick={loadRealServers}\r\n                        disabled={isImporting || backendStatus !== 'connected'}\r\n                      >\r\n                        🔄 Refresh\r\n                      </Button>\r\n                    </div>\r\n                    <Form.Select\r\n                      value={streamsServer}\r\n                      onChange={(e) => setStreamsServer(e.target.value)}\r\n                      disabled={isImporting}\r\n                    >\r\n                      <option value=\"\">Select streams server...</option>\r\n                      {availableServers.map(server => (\r\n                        <option key={server.id} value={server.id}>\r\n                          {server.name} ({server.ip}) - {server.load}\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                    <Form.Text className=\"text-muted\">\r\n                      Server where streams will be hosted and served from.\r\n                      {availableServers.length > 0 && (\r\n                        <span className=\"text-success\"> ✅ {availableServers.length} servers loaded</span>\r\n                      )}\r\n                    </Form.Text>\r\n                  </Form.Group>\r\n                )}\r\n\r\n\r\n\r\n                {streamsServer && (\r\n                  <>\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label>🔗 Source Configuration</Form.Label>\r\n                      <Form.Check \r\n                        type=\"checkbox\" \r\n                        label=\"✅ Direct Source (recommended for better performance)\"\r\n                        checked={sourceConfig.directSource}\r\n                        onChange={(e) => setSourceConfig(prev => ({\r\n                          ...prev,\r\n                          directSource: e.target.checked\r\n                        }))}\r\n                        disabled={isImporting}\r\n                      />\r\n                      <Form.Check \r\n                        type=\"checkbox\" \r\n                        label=\"🔄 Direct Proxy (for geo-restricted content)\"\r\n                        checked={sourceConfig.directProxy}\r\n                        onChange={(e) => setSourceConfig(prev => ({\r\n                          ...prev,\r\n                          directProxy: e.target.checked\r\n                        }))}\r\n                        disabled={isImporting}\r\n                      />\r\n                      <Form.Check \r\n                        type=\"checkbox\" \r\n                        label=\"⚖️ Load Balancing (distribute across servers)\"\r\n                        checked={sourceConfig.loadBalancing}\r\n                        onChange={(e) => setSourceConfig(prev => ({\r\n                          ...prev,\r\n                          loadBalancing: e.target.checked\r\n                        }))}\r\n                        disabled={isImporting}\r\n                      />\r\n                    </Form.Group>\r\n\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label>🏷️ Categories Assignment</Form.Label>\r\n                      <div style={{maxHeight: '120px', overflowY: 'auto', border: '1px solid #ddd', padding: '8px', borderRadius: '4px'}}>\r\n                        {existingCategories\r\n                          .filter(cat => !contentType || cat.type === contentType || contentType === 'live')\r\n                          .map(category => (\r\n                          <Form.Check\r\n                            key={category.id}\r\n                            type=\"checkbox\"\r\n                            label={`${category.name} (${category.type})`}\r\n                            checked={selectedCategories.includes(category.id)}\r\n                            onChange={(e) => {\r\n                              if (e.target.checked) {\r\n                                setSelectedCategories(prev => [...prev, category.id]);\r\n                              } else {\r\n                                setSelectedCategories(prev => prev.filter(id => id !== category.id));\r\n                              }\r\n                            }}\r\n                            disabled={isImporting}\r\n                          />\r\n                        ))}\r\n                      </div>\r\n                      <Form.Text className=\"text-muted\">\r\n                        Select existing categories or new ones will be created automatically\r\n                      </Form.Text>\r\n                    </Form.Group>\r\n                  </>\r\n                )}\r\n\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>⚙️ Import Settings</Form.Label>\r\n                  <Form.Check \r\n                    type=\"checkbox\" \r\n                    label=\"🔄 Auto-rename with TMDB data\"\r\n                    defaultChecked\r\n                  />\r\n                  <Form.Check \r\n                    type=\"checkbox\" \r\n                    label=\"📂 Auto-assign categories\"\r\n                    defaultChecked\r\n                  />\r\n                  <Form.Check \r\n                    type=\"checkbox\" \r\n                    label=\"🎬 Process VOD metadata\"\r\n                    defaultChecked\r\n                  />\r\n                </Form.Group>\r\n\r\n\r\n\r\n                {isImporting && (\r\n                  <div className=\"mb-3\">\r\n                    <Form.Label>Import Progress</Form.Label>\r\n                    <ProgressBar \r\n                      now={importProgress} \r\n                      label={`${importProgress}%`}\r\n                      variant={importProgress === 100 ? 'success' : 'primary'}\r\n                      animated={importProgress < 100}\r\n                    />\r\n                  </div>\r\n                )}\r\n\r\n                {selectedFile && contentType === 'series' && (\r\n                  <SeriesImportHandler\r\n                    selectedFile={selectedFile}\r\n                    isImporting={isImporting}\r\n                    onSeriesDetected={(series) => {\r\n                      window.debugLog('info', `📺 Detectadas ${series.length} series para importar`);\r\n                    }}\r\n                  />\r\n                )}\r\n\r\n                <Button \r\n                  variant=\"success\" \r\n                  size=\"lg\" \r\n                  onClick={handleImport}\r\n                  disabled={!selectedFile || !fileAnalysis || !contentType || !streamsServer || isImporting || isProcessingFile}\r\n                  className=\"w-100\"\r\n                >\r\n                  {isImporting ? (\r\n                    <>\r\n                      <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n                      Importando... {importProgress}%\r\n                    </>\r\n                  ) : !selectedFile ? (\r\n                    '📁 Selecciona un archivo M3U'\r\n                  ) : !fileAnalysis ? (\r\n                    '� Analiza el archivo primero'\r\n                  ) : !contentType || !streamsServer ? (\r\n                    '⚙️ Completa la configuración'\r\n                  ) : (\r\n                    '🚀 Iniciar Importación'\r\n                  )}\r\n                </Button>\r\n              </Form>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n\r\n        <Col lg={6}>\r\n          <Card className=\"shadow-sm h-100\">\r\n            <Card.Header className=\"bg-info text-white\">\r\n              <h5 className=\"mb-0\">ℹ️ Import Guidelines</h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <h6>📋 Supported Content Types:</h6>\r\n              <ul>\r\n                <li><strong>📺 Live Channels:</strong> TV channels with EPG support</li>\r\n                <li><strong>🎬 VOD (Movies):</strong> On-demand movie content</li>\r\n                <li><strong>📚 Series:</strong> TV series with episode management</li>\r\n                <li><strong>🎵 Radio:</strong> Audio streaming channels</li>\r\n              </ul>\r\n\r\n              <h6>⚙️ Processing Features:</h6>\r\n              <ul>\r\n                <li><strong>🎯 TMDB Integration:</strong> Auto-fetch metadata</li>\r\n                <li><strong>🏷️ Category Assignment:</strong> Smart categorization</li>\r\n                <li><strong>🖼️ Poster Download:</strong> High-quality artwork</li>\r\n                <li><strong>📝 Description Parsing:</strong> Extract show info</li>\r\n              </ul>\r\n\r\n              <h6>⚡ Performance Tips:</h6>\r\n              <ul>\r\n                <li>Files under 10MB import faster</li>\r\n                <li>Use UTF-8 encoding for special characters</li>\r\n                <li>Clean duplicate entries before import</li>\r\n                <li>Ensure stable internet for metadata fetching</li>\r\n              </ul>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      <Row>\r\n        <Col>\r\n          <Card className=\"shadow-sm\">\r\n            <Card.Header className=\"bg-secondary text-white\">\r\n              <h5 className=\"mb-0\">📊 Recent Import Queue</h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <Table striped hover>\r\n                <thead>\r\n                  <tr>\r\n                    <th>📄 File</th>\r\n                    <th>📅 Queued</th>\r\n                    <th>📊 Status</th>\r\n                    <th>🎯 Target</th>\r\n                    <th>⚡ Actions</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr>\r\n                    <td><strong>premium_list.m3u</strong></td>\r\n                    <td>2025-07-15 15:30</td>\r\n                    <td><span className=\"badge bg-warning\">⏳ Queued</span></td>\r\n                    <td>Main Server</td>\r\n                    <td>\r\n                      <Button size=\"sm\" variant=\"outline-primary\" className=\"me-1\">▶️</Button>\r\n                      <Button size=\"sm\" variant=\"outline-danger\">❌</Button>\r\n                    </td>\r\n                  </tr>\r\n                  <tr>\r\n                    <td><strong>sports_channels.m3u</strong></td>\r\n                    <td>2025-07-15 15:25</td>\r\n                    <td><span className=\"badge bg-success\">✅ Processing</span></td>\r\n                    <td>Cloud Server</td>\r\n                    <td>\r\n                      <Button size=\"sm\" variant=\"outline-info\">📊</Button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </Table>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImportM3U;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAChG,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,SACEC,iBAAiB,QACZ,sBAAsB;AAC7B,SAASC,GAAG,EAAEC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,MAAM,CAAC;;EAElD;EACA,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC;IAC/CuD,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;;EAIhE;EACA,MAAM,CAAC4D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC8D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAMgE,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMpD,iBAAiB,CAAC,CAAC;MACxC+B,gBAAgB,CAACqB,MAAM,CAACC,OAAO,GAAG,WAAW,GAAG,OAAO,CAAC;MAExD,IAAI,CAACD,MAAM,CAACC,OAAO,EAAE;QACnBC,YAAY,CAAC,SAAS,EAAE,qDAAqD,CAAC;MAChF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdxB,gBAAgB,CAAC,OAAO,CAAC;MACzBuB,YAAY,CAAC,QAAQ,EAAE,iCAAiC,CAAC;IAC3D;EACF,CAAC;EAED,MAAME,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gDAAgD5B,aAAa,EAAE,CAAC;;MAE5E;MACA,IAAIA,aAAa,KAAK,WAAW,EAAE;QACjC2B,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D,MAAMC,eAAe,CAAC,CAAC;QACvB,MAAMC,kBAAkB,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLH,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D;QACAG,YAAY,CAAC,CAAC;MAChB;IAEF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAIO,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,mCAAmCR,KAAK,CAACS,OAAO,EAAE,CAAC;MAC9E;MACA;MACAH,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MAEzD,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,sDAAsD,CAAC;MACpFT,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,UAAU,CAAC;MAE/E,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MACpCb,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEW,MAAM,CAAC;MAE1C,IAAIA,MAAM,CAAChB,OAAO,IAAIgB,MAAM,CAACE,IAAI,EAAE;QACjC,MAAMC,OAAO,GAAGH,MAAM,CAACE,IAAI,CAACE,GAAG,CAACC,MAAM,KAAK;UACzCC,EAAE,EAAED,MAAM,CAACE,SAAS;UACpBC,IAAI,EAAEH,MAAM,CAACI,WAAW,IAAI,UAAUJ,MAAM,CAACE,SAAS,EAAE;UACxDG,EAAE,EAAEL,MAAM,CAACM,SAAS,IAAI,YAAY;UACpCC,IAAI,EAAE,GAAGP,MAAM,CAACQ,aAAa,IAAI,CAAC,UAAU;UAAE;UAC9CA,aAAa,EAAER,MAAM,CAACQ,aAAa,IAAI,CAAC;UACxCf,MAAM,EAAEO,MAAM,CAACS,aAAa,KAAK,CAAC,GAAG,QAAQ,GAAG;QAClD,CAAC,CAAC,CAAC;QAEH1B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEc,OAAO,CAAC;QAChDxB,mBAAmB,CAACwB,OAAO,CAAC;QAC5Bf,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEc,OAAO,CAACY,MAAM,EAAE,YAAY,CAAC;QAErE,IAAItB,MAAM,CAACC,QAAQ,EAAE;UACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,cAAcS,OAAO,CAACY,MAAM,6BAA6B,CAAC;QACvF;MACF,CAAC,MAAM;QACL3B,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEc,MAAM,CAAC;QAChD,MAAM,IAAIgB,KAAK,CAAChB,MAAM,CAACd,KAAK,IAAI,kCAAkC,CAAC;MACrE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,IAAIO,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,gCAAgCR,KAAK,CAACS,OAAO,EAAE,CAAC;MAC3E;MACA,MAAMT,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMK,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMS,MAAM,GAAG,MAAMnE,WAAW,CAACoF,aAAa,CAAC,CAAC;MAEhD,IAAIjB,MAAM,CAAChB,OAAO,IAAIgB,MAAM,CAACE,IAAI,EAAE;QACjC,MAAMgB,UAAU,GAAGlB,MAAM,CAACE,IAAI,CAACE,GAAG,CAACe,GAAG,KAAK;UACzCb,EAAE,EAAEa,GAAG,CAACC,WAAW;UACnBZ,IAAI,EAAEW,GAAG,CAACE,aAAa;UACvBC,IAAI,EAAEC,kBAAkB,CAACJ,GAAG,CAACE,aAAa,CAAC;UAAE;UAC7CG,SAAS,EAAEL,GAAG,CAACK;QACjB,CAAC,CAAC,CAAC;QAEH3C,qBAAqB,CAACqC,UAAU,CAAC;QAEjC,IAAIzB,MAAM,CAACC,QAAQ,EAAE;UACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,cAAcwB,UAAU,CAACH,MAAM,6BAA6B,CAAC;QAC1F;MACF,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAIO,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,OAAO,EAAE,gCAAgCR,KAAK,CAACS,OAAO,EAAE,CAAC;MAC3E;MACA,MAAMT,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMqC,kBAAkB,GAAIE,YAAY,IAAK;IAC3C,MAAMjB,IAAI,GAAGiB,YAAY,CAACC,WAAW,CAAC,CAAC;IAEvC,IAAIlB,IAAI,CAACmB,QAAQ,CAAC,OAAO,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,MAAM,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC9E,OAAO,OAAO;IAChB,CAAC,MAAM,IAAInB,IAAI,CAACmB,QAAQ,CAAC,QAAQ,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,MAAM,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,OAAO,CAAC,EAAE;MACrF,OAAO,QAAQ;IACjB,CAAC,MAAM,IAAInB,IAAI,CAACmB,QAAQ,CAAC,MAAM,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,IAAI,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,SAAS,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,MAAM,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtI,OAAO,MAAM;IACf,CAAC,MAAM,IAAInB,IAAI,CAACmB,QAAQ,CAAC,OAAO,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,OAAO,CAAC,IAAInB,IAAI,CAACmB,QAAQ,CAAC,IAAI,CAAC,EAAE;MAClF,OAAO,OAAO;IAChB;IAEA,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC;;EAED;EACA,MAAMnC,YAAY,GAAGA,CAAA,KAAM;IACzBb,mBAAmB,CAAC,CAClB;MAAE2B,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,gBAAgB;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,EACnE;MAAEN,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,WAAW;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,EAC9D;MAAEN,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,aAAa;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,EAChE;MAAEN,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEE,EAAE,EAAE,eAAe;MAAEE,IAAI,EAAE;IAAM,CAAC,CACnE,CAAC;IAEF/B,qBAAqB,CAAC,CACpB;MAAEyB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEc,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAEhB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEc,IAAI,EAAE;IAAQ,CAAC,EAC/C;MAAEhB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,cAAc;MAAEc,IAAI,EAAE;IAAS,CAAC,EAC/C;MAAEhB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEc,IAAI,EAAE;IAAS,CAAC,EAChD;MAAEhB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEc,IAAI,EAAE;IAAO,CAAC,EAC9C;MAAEhB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,iBAAiB;MAAEc,IAAI,EAAE;IAAO,CAAC,EAChD;MAAEhB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,eAAe;MAAEc,IAAI,EAAE;IAAO,CAAC,EAC9C;MAAEhB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,aAAa;MAAEc,IAAI,EAAE;IAAQ,CAAC,EAC7C;MAAEhB,EAAE,EAAE,CAAC;MAAEE,IAAI,EAAE,YAAY;MAAEc,IAAI,EAAE;IAAQ,CAAC,CAC7C,CAAC;IAEF,IAAI7B,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAE,8CAA8C,CAAC;IAC5E;EACF,CAAC;EAED,MAAMkC,gBAAgB,GAAGA,CAACjC,OAAO,EAAE2B,IAAI,GAAG,MAAM,KAAK;IACnDhE,eAAe,CAACqC,OAAO,CAAC;IACxBnC,YAAY,CAAC8D,IAAI,CAAC;IAClBlE,YAAY,CAAC,IAAI,CAAC;IAClByE,UAAU,CAAC,MAAMzE,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC7C,CAAC;;EAED;EACA,MAAM6B,YAAY,GAAGA,CAACqC,IAAI,EAAE3B,OAAO,KAAK;IACtCiC,gBAAgB,CAACjC,OAAO,EAAE2B,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACjDF,SAAS,CAACT,IAAI,GAAG,MAAM;IACvBS,SAAS,CAACG,MAAM,GAAG,YAAY;IAC/BH,SAAS,CAACI,QAAQ,GAAIC,CAAC,IAAK;MAC1B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAIF,IAAI,EAAE;QACRG,gBAAgB,CAAC;UAAEF,MAAM,EAAE;YAAEC,KAAK,EAAE,CAACF,IAAI;UAAE;QAAE,CAAC,CAAC;MACjD;IACF,CAAC;IACDN,SAAS,CAACU,KAAK,CAAC,CAAC;EACnB,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC7F,YAAY,EAAE;IAEnBiB,mBAAmB,CAAC,IAAI,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACFqB,YAAY,CAAC,MAAM,EAAE,2BAA2B,CAAC;MAEjD,MAAMW,QAAQ,GAAG,MAAMhE,GAAG,CAAC+G,MAAM,CAACC,WAAW,CAAC/F,YAAY,CAAC;MAE3D,IAAI+C,QAAQ,CAACZ,OAAO,EAAE;QACpBpB,eAAe,CAACgC,QAAQ,CAACM,IAAI,CAAC;;QAE9B;QACA,MAAM2C,YAAY,GAAGC,iBAAiB,CAAClD,QAAQ,CAACM,IAAI,CAAC;QACrD,IAAI2C,YAAY,EAAE;UAAA,IAAAE,qBAAA;UAChB/E,cAAc,CAAC6E,YAAY,CAAC;UAC5B5D,YAAY,CAAC,SAAS,EAAE,oDAAoD,EAAA8D,qBAAA,GAAAnD,QAAQ,CAACM,IAAI,CAAC8C,cAAc,cAAAD,qBAAA,uBAA5BA,qBAAA,CAA8BE,iBAAiB,KAAI,CAAC,8BAA8BC,mBAAmB,CAACL,YAAY,CAAC,EAAE,CAAC;QACpM,CAAC,MAAM;UAAA,IAAAM,sBAAA;UACLlE,YAAY,CAAC,SAAS,EAAE,oDAAoD,EAAAkE,sBAAA,GAAAvD,QAAQ,CAACM,IAAI,CAAC8C,cAAc,cAAAG,sBAAA,uBAA5BA,sBAAA,CAA8BF,iBAAiB,KAAI,CAAC,YAAY,CAAC;QAC/I;MACF,CAAC,MAAM;QACL,MAAM,IAAIjC,KAAK,CAACpB,QAAQ,CAACV,KAAK,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CD,YAAY,CAAC,QAAQ,EAAE,+BAA+BC,KAAK,CAACS,OAAO,EAAE,CAAC;IACxE,CAAC,SAAS;MACR7B,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMsF,mBAAmB,GAAGA,CAAA,KAAM;IAChCxF,eAAe,CAAC,IAAI,CAAC;IACrBd,eAAe,CAAC,IAAI,CAAC;IACrBkB,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IACpBiB,YAAY,CAAC,MAAM,EAAE,iDAAiD,CAAC;EACzE,CAAC;;EAED;EACA,MAAM6D,iBAAiB,GAAIO,QAAQ,IAAK;IACtC,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAACC,iBAAiB,EAAE,OAAO,IAAI;IAEzD,MAAMC,SAAS,GAAGF,QAAQ,CAACC,iBAAiB;IAC5C,MAAME,KAAK,GAAGD,SAAS,CAACE,OAAO,GAAGF,SAAS,CAACG,MAAM,GAAGH,SAAS,CAACI,MAAM,GAAGJ,SAAS,CAACK,KAAK,GAAGL,SAAS,CAACM,OAAO;IAE3G,IAAIL,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;;IAE5B;IACA,MAAMM,WAAW,GAAG;MAClBL,OAAO,EAAGF,SAAS,CAACE,OAAO,GAAGD,KAAK,GAAI,GAAG;MAC1CE,MAAM,EAAGH,SAAS,CAACG,MAAM,GAAGF,KAAK,GAAI,GAAG;MACxCG,MAAM,EAAGJ,SAAS,CAACI,MAAM,GAAGH,KAAK,GAAI,GAAG;MACxCI,KAAK,EAAGL,SAAS,CAACK,KAAK,GAAGJ,KAAK,GAAI;IACrC,CAAC;;IAED;IACA,IAAIM,WAAW,CAACH,MAAM,GAAG,EAAE,EAAE,OAAO,QAAQ;IAC5C,IAAIG,WAAW,CAACJ,MAAM,GAAG,EAAE,EAAE,OAAO,OAAO;IAC3C,IAAII,WAAW,CAACL,OAAO,GAAG,EAAE,EAAE,OAAO,MAAM;IAC3C,IAAIK,WAAW,CAACF,KAAK,GAAG,EAAE,EAAE,OAAO,OAAO;;IAE1C;IACA,MAAMG,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACH,WAAW,CAAC,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KACnDN,WAAW,CAACK,CAAC,CAAC,GAAGL,WAAW,CAACM,CAAC,CAAC,GAAGD,CAAC,GAAGC,CACxC,CAAC;;IAED;IACA,MAAMC,OAAO,GAAG;MACdZ,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE;IACT,CAAC;IAED,OAAOS,OAAO,CAACN,OAAO,CAAC,IAAI,IAAI;EACjC,CAAC;;EAED;EACA,MAAMb,mBAAmB,GAAI5B,IAAI,IAAK;IACpC,MAAMgD,MAAM,GAAG;MACbC,KAAK,EAAE,iBAAiB;MACxBZ,MAAM,EAAE,cAAc;MACtBa,IAAI,EAAE,kBAAkB;MACxBZ,KAAK,EAAE;IACT,CAAC;IACD,OAAOU,MAAM,CAAChD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;;EAED;EACAvG,SAAS,CAAC,MAAM;IACd+D,kBAAkB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/D,SAAS,CAAC,MAAM;IACd,IAAI0C,aAAa,KAAK,UAAU,EAAE;MAChC0B,eAAe,CAAC,CAAC;IACnB;IACA;EACF,CAAC,EAAE,CAAC1B,aAAa,CAAC,CAAC;EAEnB,MAAM+E,gBAAgB,GAAG,MAAOiC,KAAK,IAAK;IACxC,MAAMpC,IAAI,GAAGoC,KAAK,CAACnC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClCzF,eAAe,CAACuF,IAAI,CAAC;IACrBzE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAIyE,IAAI,EAAE;MACRpD,YAAY,CAAC,MAAM,EAAE,yBAAyBoD,IAAI,CAAC7B,IAAI,KAAK,CAAC6B,IAAI,CAACqC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;;MAEvG;MACA,IAAIlF,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,qBAAqB2C,IAAI,CAAC7B,IAAI,EAAE,EAAE,MAAM,CAAC;QACzDf,MAAM,CAACC,QAAQ,CAAC,iBAAiB,CAAC2C,IAAI,CAACqC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC;MACrF;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC/H,YAAY,IAAI,CAACkB,WAAW,IAAI,CAACE,aAAa,EAAE;MACnDgB,YAAY,CAAC,SAAS,EAAE,4FAA4F,CAAC;MACrH;IACF;IAIA,IAAI,CAACtB,YAAY,EAAE;MACjBsB,YAAY,CAAC,SAAS,EAAE,oDAAoD,CAAC;MAC7E;IACF;IAEA,IAAIQ,MAAM,CAACC,QAAQ,EAAE;MACnBD,MAAM,CAACC,QAAQ,CAAC,yBAAyB7C,YAAY,CAAC2D,IAAI,EAAE,EAAE,MAAM,CAAC;MACrEf,MAAM,CAACC,QAAQ,CAAC,iBAAiB,CAAC7C,YAAY,CAAC6H,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC;MAC3FlF,MAAM,CAACC,QAAQ,CAAC,oBAAoB3B,WAAW,EAAE,EAAE,MAAM,CAAC;MAC1D0B,MAAM,CAACC,QAAQ,CAAC,sBAAsBzB,aAAa,EAAE,EAAE,MAAM,CAAC;IAChE;IAEAjB,cAAc,CAAC,IAAI,CAAC;IACpBE,iBAAiB,CAAC,CAAC,CAAC;IAEpB,IAAI;MACF;MACA,MAAM2H,YAAY,GAAG;QACnB9G,WAAW;QACXE,aAAa;QACbE,YAAY;QACZ+C,UAAU,EAAE1C,kBAAkB;QAC9BsG,WAAW,EAAE,IAAI;QACjBC,oBAAoB,EAAE;MACxB,CAAC;MAED9F,YAAY,CAAC,MAAM,EAAE,wCAAwC,CAAC;MAC9D/B,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,MAAM8H,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAErI,YAAY,CAAC;MACrCmI,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAACP,YAAY,CAAC,CAAC;MAEvD,IAAIpF,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,iCAAiC,EAAE,MAAM,CAAC;MAC5D;;MAEA;MACA,MAAM2F,eAAe,GAAG,MAAMzJ,GAAG,CAAC+G,MAAM,CAACC,WAAW,CAAC/F,YAAY,CAAC;MAClEK,iBAAiB,CAAC,EAAE,CAAC;MAErB,IAAI,CAACmI,eAAe,CAACrG,OAAO,EAAE;QAC5B,MAAM,IAAIgC,KAAK,CAACqE,eAAe,CAACnG,KAAK,IAAI,sBAAsB,CAAC;MAClE;MAEAD,YAAY,CAAC,MAAM,EAAE,4CAA4C,CAAC;MAClE;MACA,MAAMqG,WAAW,GAAG,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACzD,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAIxD,CAAC,IAAKoD,OAAO,CAACpD,CAAC,CAACE,MAAM,CAACtC,MAAM,CAAC;QAC/C0F,MAAM,CAACG,OAAO,GAAG,MAAMJ,MAAM,CAAC,IAAIzE,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC9D0E,MAAM,CAACI,UAAU,CAACjJ,YAAY,CAAC;MACjC,CAAC,CAAC;MAEFK,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,MAAM6I,aAAa,GAAG,MAAMlG,KAAK,CAAC,gDAAgD,EAAE;QAClFmG,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEf,IAAI,CAACC,SAAS,CAAC;UACnBe,UAAU,EAAEb,WAAW;UACvBvH,WAAW,EAAEA;QACf,CAAC;MACH,CAAC,CAAC;MAEF,MAAMqI,WAAW,GAAG,MAAML,aAAa,CAAC9F,IAAI,CAAC,CAAC;MAC9C,IAAI,CAACmG,WAAW,CAACpH,OAAO,EAAE;QACxB,MAAM,IAAIgC,KAAK,CAACoF,WAAW,CAAClH,KAAK,IAAI,uBAAuB,CAAC;MAC/D;MAEAhC,iBAAiB,CAAC,EAAE,CAAC;;MAErB;MACA,MAAMmJ,aAAa,GAAG;QACpB9F,SAAS,EAAE+F,QAAQ,CAACrI,aAAa,CAAC;QAClCmD,WAAW,EAAE5C,kBAAkB,CAACuC,MAAM,GAAG,CAAC,GAAGuF,QAAQ,CAAC9H,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;QACnF+H,WAAW,EAAEpI,YAAY,CAACqI,cAAc,IAAI,IAAI;QAChDzI,WAAW,EAAEA;MACf,CAAC;;MAED;MACA,IAAIA,WAAW,KAAK,QAAQ,EAAE;QAC5BsI,aAAa,CAACI,QAAQ,GAAGL,WAAW,CAAClG,IAAI,CAACuG,QAAQ;MACpD,CAAC,MAAM,IAAI1I,WAAW,KAAK,OAAO,EAAE;QAClCsI,aAAa,CAAC3C,MAAM,GAAG0C,WAAW,CAAClG,IAAI,CAACwD,MAAM;MAChD;MAEA,MAAMgD,cAAc,GAAG,MAAM7G,KAAK,CAAC,0CAA0C,EAAE;QAC7EmG,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEf,IAAI,CAACC,SAAS,CAACiB,aAAa;MACpC,CAAC,CAAC;MAEF,MAAMM,YAAY,GAAG,MAAMD,cAAc,CAACzG,IAAI,CAAC,CAAC;MAChD,IAAI,CAAC0G,YAAY,CAAC3H,OAAO,EAAE;QACzB,MAAM,IAAIgC,KAAK,CAAC2F,YAAY,CAACzH,KAAK,IAAI,yBAAyB,CAAC;MAClE;;MAEA;MACAhC,iBAAiB,CAAC,GAAG,CAAC;;MAEtB;MACA,IAAI0J,cAAc,GAAG,+DAA+DD,YAAY,CAACE,QAAQ,4BAA4BF,YAAY,CAACG,MAAM,UAAU;MAElK,IAAI/I,WAAW,KAAK,QAAQ,EAAE;QAC5B6I,cAAc,IAAI,OAAOD,YAAY,CAACI,cAAc,sBAAsBJ,YAAY,CAACK,gBAAgB,oBAAoB;MAC7H,CAAC,MAAM,IAAIjJ,WAAW,KAAK,OAAO,EAAE;QAClC6I,cAAc,IAAI,OAAOD,YAAY,CAACM,cAAc,oBAAoB;MAC1E;MAEAhI,YAAY,CAAC,SAAS,EAAE2H,cAAc,CAAC;MAEvC,IAAInH,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,oCAAoC7C,YAAY,CAAC2D,IAAI,EAAE,EAAE,SAAS,CAAC;QACnFf,MAAM,CAACC,QAAQ,CAAC,aAAayF,IAAI,CAACC,SAAS,CAACuB,YAAY,CAAC,EAAE,EAAE,MAAM,CAAC;MACtE;;MAEA;MACA9E,UAAU,CAAC,MAAM;QACf/E,eAAe,CAAC,IAAI,CAAC;QACrBc,eAAe,CAAC,IAAI,CAAC;QACrBI,cAAc,CAAC,EAAE,CAAC;QAClBE,gBAAgB,CAAC,EAAE,CAAC;QACpBO,qBAAqB,CAAC,EAAE,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCD,YAAY,CAAC,QAAQ,EAAE,mCAAmCC,KAAK,CAACS,OAAO,EAAE,CAAC;MAE1E,IAAIF,MAAM,CAACC,QAAQ,EAAE;QACnBD,MAAM,CAACC,QAAQ,CAAC,oBAAoBR,KAAK,CAACS,OAAO,EAAE,EAAE,OAAO,CAAC;MAC/D;IACF,CAAC,SAAS;MACR3C,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEjB,OAAA;IAAKmL,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9CtL,OAAA;MAAKuL,SAAS,EAAC,wDAAwD;MAAAD,QAAA,gBACrEtL,OAAA;QAAIuL,SAAS,EAAC,cAAc;QAAAD,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrD3L,OAAA,CAACL,aAAa;QACZoE,MAAM,EAAErC,aAAc;QACtBkK,OAAO,EAAE7I;MAAmB;QAAAyI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELvK,SAAS,iBACRpB,OAAA,CAACV,KAAK;MAACuM,OAAO,EAAErK,SAAU;MAACsK,WAAW;MAACC,OAAO,EAAEA,CAAA,KAAM1K,YAAY,CAAC,KAAK,CAAE;MAAAiK,QAAA,gBACxEtL,OAAA,CAACV,KAAK,CAAC0M,OAAO;QAAAV,QAAA,GACX9J,SAAS,KAAK,SAAS,IAAI,YAAY,EACvCA,SAAS,KAAK,QAAQ,IAAI,UAAU,EACpCA,SAAS,KAAK,SAAS,IAAI,aAAa,EACxCA,SAAS,KAAK,MAAM,IAAI,gBAAgB;MAAA;QAAAgK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAChB3L,OAAA;QAAAsL,QAAA,EAAIhK;MAAY;QAAAkK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACR,eAED3L,OAAA,CAACf,GAAG;MAACsM,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBtL,OAAA,CAACd,GAAG;QAAC+M,EAAE,EAAE,CAAE;QAAAX,QAAA,eACTtL,OAAA,CAACb,IAAI;UAACoM,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC/BtL,OAAA,CAACb,IAAI,CAAC+M,MAAM;YAACX,SAAS,EAAC,yEAAyE;YAAAD,QAAA,gBAC9FtL,OAAA;cAAIuL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvCjK,aAAa,KAAK,WAAW,iBAC5B1B,OAAA,CAACP,KAAK;cAAC0M,EAAE,EAAC,SAAS;cAAAb,QAAA,gBACjBtL,OAAA;gBAAGuL,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBACvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACd3L,OAAA,CAACb,IAAI,CAACiN,IAAI;YAAAd,QAAA,eACRtL,OAAA,CAACX,IAAI;cAAAiM,QAAA,gBACHtL,OAAA,CAACX,IAAI,CAACgN,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BtL,OAAA,CAACX,IAAI,CAACiN,KAAK;kBAAAhB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC3L,OAAA,CAACX,IAAI,CAACkN,OAAO;kBACXhH,IAAI,EAAC,MAAM;kBACXY,MAAM,EAAC,YAAY;kBACnBqG,QAAQ,EAAE/F,gBAAiB;kBAC3BgG,QAAQ,EAAEzL;gBAAY;kBAAAwK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACF3L,OAAA,CAACX,IAAI,CAACqN,IAAI;kBAACnB,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAElC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAEZ7K,YAAY,iBACXd,OAAA,CAAAE,SAAA;gBAAAoL,QAAA,gBACEtL,OAAA,CAACV,KAAK;kBAACuM,OAAO,EAAC,MAAM;kBAAAP,QAAA,gBACnBtL,OAAA;oBAAAsL,QAAA,EAAQ;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC7K,YAAY,CAAC2D,IAAI,eAACzE,OAAA;oBAAAwL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxD3L,OAAA;oBAAAsL,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,CAAC7K,YAAY,CAAC6H,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACvE;gBAAA;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAEP7J,gBAAgB,iBACf9B,OAAA,CAACV,KAAK;kBAACuM,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACxBtL,OAAA;oBAAKuL,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,gBACxCtL,OAAA;sBAAKuL,SAAS,EAAC,uCAAuC;sBAACoB,IAAI,EAAC;oBAAQ;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,yBAE7E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR,EAEA/J,YAAY,IAAI,CAACE,gBAAgB,iBAChC9B,OAAA,CAACV,KAAK;kBAACuM,OAAO,EAAC,SAAS;kBAAAP,QAAA,gBACtBtL,OAAA;oBAAAsL,QAAA,EAAI;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChC3L,OAAA,CAACf,GAAG;oBAAAqM,QAAA,gBACFtL,OAAA,CAACd,GAAG;sBAAC0N,EAAE,EAAE,CAAE;sBAAAtB,QAAA,gBACTtL,OAAA;wBAAAsL,QAAA,EAAQ;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAtL,qBAAA,GAAAuB,YAAY,CAACqF,cAAc,cAAA5G,qBAAA,uBAA3BA,qBAAA,CAA6BwM,WAAW,KAAI,CAAC,eAAC7M,OAAA;wBAAAwL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClF3L,OAAA;wBAAAsL,QAAA,EAAQ;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAArL,sBAAA,GAAAsB,YAAY,CAACqF,cAAc,cAAA3G,sBAAA,uBAA3BA,sBAAA,CAA6BwM,YAAY,KAAI,CAAC,eAAC9M,OAAA;wBAAAwL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACtF3L,OAAA;wBAAAsL,QAAA,EAAQ;sBAAY;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAApL,sBAAA,GAAAqB,YAAY,CAACqF,cAAc,cAAA1G,sBAAA,uBAA3BA,sBAAA,CAA6BwM,SAAS,KAAI,CAAC;oBAAA;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACN3L,OAAA,CAACd,GAAG;sBAAC0N,EAAE,EAAE,CAAE;sBAAAtB,QAAA,gBACTtL,OAAA;wBAAAsL,QAAA,EAAQ;sBAAkB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAnL,sBAAA,GAAAoB,YAAY,CAACqF,cAAc,cAAAzG,sBAAA,uBAA3BA,sBAAA,CAA6B0G,iBAAiB,KAAI,CAAC,eAAClH,OAAA;wBAAAwL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC9F3L,OAAA;wBAAAsL,QAAA,EAAQ;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,CAAAlL,sBAAA,GAAAmB,YAAY,CAACqF,cAAc,cAAAxG,sBAAA,eAA3BA,sBAAA,CAA6BuM,oBAAoB,GAAG,OAAO,GAAG,MAAM,eAAChN,OAAA;wBAAAwL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACvG3L,OAAA;wBAAAsL,QAAA,EAAQ;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAjL,qBAAA,GAAAkB,YAAY,CAACqL,SAAS,cAAAvM,qBAAA,uBAAtBA,qBAAA,CAAwBwM,OAAO,KAAI,CAAC,EAAC,KACpE;oBAAA;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,EAAAhL,qBAAA,GAAAiB,YAAY,CAACuL,aAAa,cAAAxM,qBAAA,wBAAAC,sBAAA,GAA1BD,qBAAA,CAA4BiH,MAAM,cAAAhH,sBAAA,uBAAlCA,sBAAA,CAAoCqC,OAAO,kBAC1CjD,OAAA;oBAAKuL,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBACnBtL,OAAA;sBAAAsL,QAAA,EAAQ;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA9K,sBAAA,GAAAe,YAAY,CAACuL,aAAa,CAACvF,MAAM,CAACzD,IAAI,cAAAtD,sBAAA,uBAAtCA,sBAAA,CAAwCmE,MAAM,KAAI,CAAC;kBAAA;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACR;cAAA,eACD,CACH,eAED3L,OAAA;gBAAKuL,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBtL,OAAA,CAACZ,MAAM;kBACLgO,OAAO,EAAErH,iBAAkB;kBAC3B8F,OAAO,EAAC,SAAS;kBACjBY,QAAQ,EAAE3K,gBAAiB;kBAAAwJ,QAAA,EAC5B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAER7K,YAAY,IAAI,CAACc,YAAY,iBAC5B5B,OAAA,CAACZ,MAAM;kBACLgO,OAAO,EAAEzG,iBAAkB;kBAC3BkF,OAAO,EAAC,MAAM;kBACdN,SAAS,EAAC,MAAM;kBAChBkB,QAAQ,EAAE3K,gBAAiB;kBAAAwJ,QAAA,EAE1BxJ,gBAAgB,gBACf9B,OAAA,CAAAE,SAAA;oBAAAoL,QAAA,gBACEtL,OAAA;sBAAMuL,SAAS,EAAC,uCAAuC;sBAACoB,IAAI,EAAC;oBAAQ;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,iBAE/E;kBAAA,eAAE,CAAC,GAEH;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CACT,EAEA/J,YAAY,iBACX5B,OAAA,CAACZ,MAAM;kBACLgO,OAAO,EAAE/F,mBAAoB;kBAC7BwE,OAAO,EAAC,mBAAmB;kBAC3BN,SAAS,EAAC,MAAM;kBAChBkB,QAAQ,EAAE3K,gBAAiB;kBAAAwJ,QAAA,EAC5B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3L,OAAA,CAACX,IAAI,CAACgN,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BtL,OAAA,CAACX,IAAI,CAACiN,KAAK;kBAAAhB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC3L,OAAA,CAACX,IAAI,CAACgO,MAAM;kBACVC,KAAK,EAAEtL,WAAY;kBACnBwK,QAAQ,EAAGnG,CAAC,IAAKpE,cAAc,CAACoE,CAAC,CAACE,MAAM,CAAC+G,KAAK,CAAE;kBAChDb,QAAQ,EAAEzL,WAAY;kBAAAsK,QAAA,gBAEtBtL,OAAA;oBAAQsN,KAAK,EAAC,EAAE;oBAAAhC,QAAA,EAAC;kBAAsB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD3L,OAAA;oBAAQsN,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9C3L,OAAA;oBAAQsN,KAAK,EAAC,QAAQ;oBAAAhC,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C3L,OAAA;oBAAQsN,KAAK,EAAC,MAAM;oBAAAhC,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9C3L,OAAA;oBAAQsN,KAAK,EAAC,OAAO;oBAAAhC,QAAA,EAAC;kBAAiB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACd3L,OAAA,CAACX,IAAI,CAACqN,IAAI;kBAACnB,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAAC;gBAElC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAEZ3J,WAAW,iBACVhC,OAAA,CAACX,IAAI,CAACgN,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BtL,OAAA;kBAAKuL,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,gBACrEtL,OAAA,CAACX,IAAI,CAACiN,KAAK;oBAACf,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnE3L,OAAA,CAACZ,MAAM;oBACLyM,OAAO,EAAC,mBAAmB;oBAC3BlD,IAAI,EAAC,IAAI;oBACTyE,OAAO,EAAE7J,eAAgB;oBACzBkJ,QAAQ,EAAEzL,WAAW,IAAIU,aAAa,KAAK,WAAY;oBAAA4J,QAAA,EACxD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN3L,OAAA,CAACX,IAAI,CAACgO,MAAM;kBACVC,KAAK,EAAEpL,aAAc;kBACrBsK,QAAQ,EAAGnG,CAAC,IAAKlE,gBAAgB,CAACkE,CAAC,CAACE,MAAM,CAAC+G,KAAK,CAAE;kBAClDb,QAAQ,EAAEzL,WAAY;kBAAAsK,QAAA,gBAEtBtL,OAAA;oBAAQsN,KAAK,EAAC,EAAE;oBAAAhC,QAAA,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACjDhJ,gBAAgB,CAAC0B,GAAG,CAACC,MAAM,iBAC1BtE,OAAA;oBAAwBsN,KAAK,EAAEhJ,MAAM,CAACC,EAAG;oBAAA+G,QAAA,GACtChH,MAAM,CAACG,IAAI,EAAC,IAAE,EAACH,MAAM,CAACK,EAAE,EAAC,MAAI,EAACL,MAAM,CAACO,IAAI;kBAAA,GAD/BP,MAAM,CAACC,EAAE;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eACd3L,OAAA,CAACX,IAAI,CAACqN,IAAI;kBAACnB,SAAS,EAAC,YAAY;kBAAAD,QAAA,GAAC,sDAEhC,EAAC3I,gBAAgB,CAACqC,MAAM,GAAG,CAAC,iBAC1BhF,OAAA;oBAAMuL,SAAS,EAAC,cAAc;oBAAAD,QAAA,GAAC,UAAG,EAAC3I,gBAAgB,CAACqC,MAAM,EAAC,iBAAe;kBAAA;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACjF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACb,EAIAzJ,aAAa,iBACZlC,OAAA,CAAAE,SAAA;gBAAAoL,QAAA,gBACEtL,OAAA,CAACX,IAAI,CAACgN,KAAK;kBAACd,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BtL,OAAA,CAACX,IAAI,CAACiN,KAAK;oBAAAhB,QAAA,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChD3L,OAAA,CAACX,IAAI,CAACkO,KAAK;oBACThI,IAAI,EAAC,UAAU;oBACfiI,KAAK,EAAC,2DAAsD;oBAC5DC,OAAO,EAAErL,YAAY,CAACE,YAAa;oBACnCkK,QAAQ,EAAGnG,CAAC,IAAKhE,eAAe,CAACqL,IAAI,KAAK;sBACxC,GAAGA,IAAI;sBACPpL,YAAY,EAAE+D,CAAC,CAACE,MAAM,CAACkH;oBACzB,CAAC,CAAC,CAAE;oBACJhB,QAAQ,EAAEzL;kBAAY;oBAAAwK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACF3L,OAAA,CAACX,IAAI,CAACkO,KAAK;oBACThI,IAAI,EAAC,UAAU;oBACfiI,KAAK,EAAC,wDAA8C;oBACpDC,OAAO,EAAErL,YAAY,CAACG,WAAY;oBAClCiK,QAAQ,EAAGnG,CAAC,IAAKhE,eAAe,CAACqL,IAAI,KAAK;sBACxC,GAAGA,IAAI;sBACPnL,WAAW,EAAE8D,CAAC,CAACE,MAAM,CAACkH;oBACxB,CAAC,CAAC,CAAE;oBACJhB,QAAQ,EAAEzL;kBAAY;oBAAAwK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACF3L,OAAA,CAACX,IAAI,CAACkO,KAAK;oBACThI,IAAI,EAAC,UAAU;oBACfiI,KAAK,EAAC,yDAA+C;oBACrDC,OAAO,EAAErL,YAAY,CAACI,aAAc;oBACpCgK,QAAQ,EAAGnG,CAAC,IAAKhE,eAAe,CAACqL,IAAI,KAAK;sBACxC,GAAGA,IAAI;sBACPlL,aAAa,EAAE6D,CAAC,CAACE,MAAM,CAACkH;oBAC1B,CAAC,CAAC,CAAE;oBACJhB,QAAQ,EAAEzL;kBAAY;oBAAAwK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEb3L,OAAA,CAACX,IAAI,CAACgN,KAAK;kBAACd,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BtL,OAAA,CAACX,IAAI,CAACiN,KAAK;oBAAAhB,QAAA,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClD3L,OAAA;oBAAKmL,KAAK,EAAE;sBAACwC,SAAS,EAAE,OAAO;sBAAEC,SAAS,EAAE,MAAM;sBAAEC,MAAM,EAAE,gBAAgB;sBAAEC,OAAO,EAAE,KAAK;sBAAEC,YAAY,EAAE;oBAAK,CAAE;oBAAAzC,QAAA,EAChHzI,kBAAkB,CAChBmL,MAAM,CAAC5I,GAAG,IAAI,CAACpD,WAAW,IAAIoD,GAAG,CAACG,IAAI,KAAKvD,WAAW,IAAIA,WAAW,KAAK,MAAM,CAAC,CACjFqC,GAAG,CAAC4J,QAAQ,iBACbjO,OAAA,CAACX,IAAI,CAACkO,KAAK;sBAEThI,IAAI,EAAC,UAAU;sBACfiI,KAAK,EAAE,GAAGS,QAAQ,CAACxJ,IAAI,KAAKwJ,QAAQ,CAAC1I,IAAI,GAAI;sBAC7CkI,OAAO,EAAEhL,kBAAkB,CAACmD,QAAQ,CAACqI,QAAQ,CAAC1J,EAAE,CAAE;sBAClDiI,QAAQ,EAAGnG,CAAC,IAAK;wBACf,IAAIA,CAAC,CAACE,MAAM,CAACkH,OAAO,EAAE;0BACpB/K,qBAAqB,CAACgL,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEO,QAAQ,CAAC1J,EAAE,CAAC,CAAC;wBACvD,CAAC,MAAM;0BACL7B,qBAAqB,CAACgL,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACzJ,EAAE,IAAIA,EAAE,KAAK0J,QAAQ,CAAC1J,EAAE,CAAC,CAAC;wBACtE;sBACF,CAAE;sBACFkI,QAAQ,EAAEzL;oBAAY,GAXjBiN,QAAQ,CAAC1J,EAAE;sBAAAiH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAYjB,CACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN3L,OAAA,CAACX,IAAI,CAACqN,IAAI;oBAACnB,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAElC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,eACb,CACH,eAED3L,OAAA,CAACX,IAAI,CAACgN,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BtL,OAAA,CAACX,IAAI,CAACiN,KAAK;kBAAAhB,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3C3L,OAAA,CAACX,IAAI,CAACkO,KAAK;kBACThI,IAAI,EAAC,UAAU;kBACfiI,KAAK,EAAC,yCAA+B;kBACrCU,cAAc;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACF3L,OAAA,CAACX,IAAI,CAACkO,KAAK;kBACThI,IAAI,EAAC,UAAU;kBACfiI,KAAK,EAAC,qCAA2B;kBACjCU,cAAc;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACF3L,OAAA,CAACX,IAAI,CAACkO,KAAK;kBACThI,IAAI,EAAC,UAAU;kBACfiI,KAAK,EAAC,mCAAyB;kBAC/BU,cAAc;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,EAIZ3K,WAAW,iBACVhB,OAAA;gBAAKuL,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBtL,OAAA,CAACX,IAAI,CAACiN,KAAK;kBAAAhB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC3L,OAAA,CAACT,WAAW;kBACV4O,GAAG,EAAEjN,cAAe;kBACpBsM,KAAK,EAAE,GAAGtM,cAAc,GAAI;kBAC5B2K,OAAO,EAAE3K,cAAc,KAAK,GAAG,GAAG,SAAS,GAAG,SAAU;kBACxDkN,QAAQ,EAAElN,cAAc,GAAG;gBAAI;kBAAAsK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EAEA7K,YAAY,IAAIkB,WAAW,KAAK,QAAQ,iBACvChC,OAAA,CAACN,mBAAmB;gBAClBoB,YAAY,EAAEA,YAAa;gBAC3BE,WAAW,EAAEA,WAAY;gBACzBqN,gBAAgB,EAAGzG,MAAM,IAAK;kBAC5BlE,MAAM,CAACC,QAAQ,CAAC,MAAM,EAAE,iBAAiBiE,MAAM,CAAC5C,MAAM,uBAAuB,CAAC;gBAChF;cAAE;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,eAED3L,OAAA,CAACZ,MAAM;gBACLyM,OAAO,EAAC,SAAS;gBACjBlD,IAAI,EAAC,IAAI;gBACTyE,OAAO,EAAEvE,YAAa;gBACtB4D,QAAQ,EAAE,CAAC3L,YAAY,IAAI,CAACc,YAAY,IAAI,CAACI,WAAW,IAAI,CAACE,aAAa,IAAIlB,WAAW,IAAIc,gBAAiB;gBAC9GyJ,SAAS,EAAC,OAAO;gBAAAD,QAAA,EAEhBtK,WAAW,gBACVhB,OAAA,CAAAE,SAAA;kBAAAoL,QAAA,gBACEtL,OAAA;oBAAMuL,SAAS,EAAC,uCAAuC;oBAACoB,IAAI,EAAC;kBAAQ;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,kBAC/D,EAACzK,cAAc,EAAC,GAChC;gBAAA,eAAE,CAAC,GACD,CAACJ,YAAY,GACf,8BAA8B,GAC5B,CAACc,YAAY,GACf,8BAA8B,GAC5B,CAACI,WAAW,IAAI,CAACE,aAAa,GAChC,8BAA8B,GAE9B;cACD;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN3L,OAAA,CAACd,GAAG;QAAC+M,EAAE,EAAE,CAAE;QAAAX,QAAA,eACTtL,OAAA,CAACb,IAAI;UAACoM,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC/BtL,OAAA,CAACb,IAAI,CAAC+M,MAAM;YAACX,SAAS,EAAC,oBAAoB;YAAAD,QAAA,eACzCtL,OAAA;cAAIuL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACd3L,OAAA,CAACb,IAAI,CAACiN,IAAI;YAAAd,QAAA,gBACRtL,OAAA;cAAAsL,QAAA,EAAI;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpC3L,OAAA;cAAAsL,QAAA,gBACEtL,OAAA;gBAAAsL,QAAA,gBAAItL,OAAA;kBAAAsL,QAAA,EAAQ;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,iCAA6B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxE3L,OAAA;gBAAAsL,QAAA,gBAAItL,OAAA;kBAAAsL,QAAA,EAAQ;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,4BAAwB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClE3L,OAAA;gBAAAsL,QAAA,gBAAItL,OAAA;kBAAAsL,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,sCAAkC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtE3L,OAAA;gBAAAsL,QAAA,gBAAItL,OAAA;kBAAAsL,QAAA,EAAQ;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,6BAAyB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eAEL3L,OAAA;cAAAsL,QAAA,EAAI;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChC3L,OAAA;cAAAsL,QAAA,gBACEtL,OAAA;gBAAAsL,QAAA,gBAAItL,OAAA;kBAAAsL,QAAA,EAAQ;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,wBAAoB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClE3L,OAAA;gBAAAsL,QAAA,gBAAItL,OAAA;kBAAAsL,QAAA,EAAQ;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,yBAAqB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvE3L,OAAA;gBAAAsL,QAAA,gBAAItL,OAAA;kBAAAsL,QAAA,EAAQ;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,yBAAqB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnE3L,OAAA;gBAAAsL,QAAA,gBAAItL,OAAA;kBAAAsL,QAAA,EAAQ;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,sBAAkB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eAEL3L,OAAA;cAAAsL,QAAA,EAAI;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5B3L,OAAA;cAAAsL,QAAA,gBACEtL,OAAA;gBAAAsL,QAAA,EAAI;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvC3L,OAAA;gBAAAsL,QAAA,EAAI;cAAyC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClD3L,OAAA;gBAAAsL,QAAA,EAAI;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C3L,OAAA;gBAAAsL,QAAA,EAAI;cAA4C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3L,OAAA,CAACf,GAAG;MAAAqM,QAAA,eACFtL,OAAA,CAACd,GAAG;QAAAoM,QAAA,eACFtL,OAAA,CAACb,IAAI;UAACoM,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACzBtL,OAAA,CAACb,IAAI,CAAC+M,MAAM;YAACX,SAAS,EAAC,yBAAyB;YAAAD,QAAA,eAC9CtL,OAAA;cAAIuL,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACd3L,OAAA,CAACb,IAAI,CAACiN,IAAI;YAAAd,QAAA,eACRtL,OAAA,CAACR,KAAK;cAAC8O,OAAO;cAACC,KAAK;cAAAjD,QAAA,gBAClBtL,OAAA;gBAAAsL,QAAA,eACEtL,OAAA;kBAAAsL,QAAA,gBACEtL,OAAA;oBAAAsL,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB3L,OAAA;oBAAAsL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB3L,OAAA;oBAAAsL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB3L,OAAA;oBAAAsL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB3L,OAAA;oBAAAsL,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR3L,OAAA;gBAAAsL,QAAA,gBACEtL,OAAA;kBAAAsL,QAAA,gBACEtL,OAAA;oBAAAsL,QAAA,eAAItL,OAAA;sBAAAsL,QAAA,EAAQ;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1C3L,OAAA;oBAAAsL,QAAA,EAAI;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzB3L,OAAA;oBAAAsL,QAAA,eAAItL,OAAA;sBAAMuL,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3D3L,OAAA;oBAAAsL,QAAA,EAAI;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpB3L,OAAA;oBAAAsL,QAAA,gBACEtL,OAAA,CAACZ,MAAM;sBAACuJ,IAAI,EAAC,IAAI;sBAACkD,OAAO,EAAC,iBAAiB;sBAACN,SAAS,EAAC,MAAM;sBAAAD,QAAA,EAAC;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxE3L,OAAA,CAACZ,MAAM;sBAACuJ,IAAI,EAAC,IAAI;sBAACkD,OAAO,EAAC,gBAAgB;sBAAAP,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACL3L,OAAA;kBAAAsL,QAAA,gBACEtL,OAAA;oBAAAsL,QAAA,eAAItL,OAAA;sBAAAsL,QAAA,EAAQ;oBAAmB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7C3L,OAAA;oBAAAsL,QAAA,EAAI;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzB3L,OAAA;oBAAAsL,QAAA,eAAItL,OAAA;sBAAMuL,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/D3L,OAAA;oBAAAsL,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrB3L,OAAA;oBAAAsL,QAAA,eACEtL,OAAA,CAACZ,MAAM;sBAACuJ,IAAI,EAAC,IAAI;sBAACkD,OAAO,EAAC,cAAc;sBAAAP,QAAA,EAAC;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvL,EAAA,CAz2BID,SAAS;AAAAqO,EAAA,GAATrO,SAAS;AA22Bf,eAAeA,SAAS;AAAC,IAAAqO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}