@echo off
title XUI IMPORTER
cls

echo.
echo ================================
echo     XUI IMPORTER - INICIANDO
echo ================================
echo.

REM Ir al directorio correcto
cd /d "%~dp0"

REM Matar procesos anteriores
taskkill /f /im node.exe >nul 2>&1

echo Limpiando procesos anteriores...
timeout /t 2 /nobreak >nul

REM Verificar Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no instalado
    echo Instala Node.js desde: https://nodejs.org/
    pause
    exit
)

echo Node.js OK
echo.

REM Instalar dependencias si no existen
if not exist "node_modules" (
    echo Instalando dependencias...
    npm install
)

echo.
echo Iniciando Backend...
start /min cmd /c "cd backend && node simple-server.js"

echo Esperando 5 segundos...
timeout /t 5 /nobreak >nul

echo Iniciando Frontend...
start /min cmd /c "npm start"

echo Esperando 10 segundos...
timeout /t 10 /nobreak >nul

echo.
echo Abriendo navegador...
start http://localhost:3000

echo.
echo ================================
echo     APLICACION INICIADA
echo ================================
echo.
echo Frontend: http://localhost:3000
echo Backend:  http://localhost:5001
echo.
echo Presiona cualquier tecla para cerrar...
pause >nul

echo.
echo Cerrando aplicacion...
taskkill /f /im node.exe >nul 2>&1
echo Listo.
timeout /t 2 /nobreak >nul
