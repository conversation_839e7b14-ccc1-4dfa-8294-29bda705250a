@echo off
echo.
echo ========================================
echo    🚀 XUI IMPORTER - BACKEND SERVER
echo ========================================
echo.

cd /d "%~dp0backend"

echo 📍 Directorio actual: %CD%
echo.

echo 🔍 Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js no está instalado o no está en el PATH
    echo 💡 Instala Node.js desde: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js encontrado: 
node --version
echo.

echo 🔍 Verificando archivo del servidor...
if not exist "simple-server.js" (
    echo ❌ No se encontró simple-server.js
    echo 📍 Asegúrate de estar en el directorio correcto
    pause
    exit /b 1
)

echo ✅ Archivo del servidor encontrado
echo.

echo 🚀 Iniciando servidor backend...
echo 🌐 El servidor estará disponible en: http://localhost:5001
echo 🛑 Presiona Ctrl+C para detener el servidor
echo.
echo ========================================
echo.

node simple-server.js

echo.
echo 🛑 Servidor detenido
pause
