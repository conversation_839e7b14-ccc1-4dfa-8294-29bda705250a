@echo off
title XUI IMPORTER - Inicio Simple
cls

echo.
echo ================================
echo     XUI IMPORTER - SIMPLE
echo ================================
echo.

cd /d "F:\WORKSPACE\XUI IMPORTER\xui-importer"

echo Matando procesos anteriores...
taskkill /f /im node.exe >nul 2>&1

echo Iniciando Backend...
start "Backend" cmd /k "cd backend && node simple-server.js"

echo Esperando 5 segundos...
timeout /t 5 /nobreak >nul

echo Iniciando Frontend...
start "Frontend" cmd /k "npm start"

echo Esperando 10 segundos...
timeout /t 10 /nobreak >nul

echo Abriendo navegador...
start http://localhost:3000

echo.
echo ================================
echo     APLICACION INICIADA
echo ================================
echo.
echo Frontend: http://localhost:3000
echo Backend:  http://localhost:5001
echo.
echo Presiona cualquier tecla para cerrar...
pause >nul

taskkill /f /im node.exe >nul 2>&1
echo Aplicacion cerrada.
