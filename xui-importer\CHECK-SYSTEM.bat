@echo off
title XUI IMPORTER - Verificación del Sistema

echo.
echo ========================================
echo    🔍 XUI IMPORTER - VERIFICACIÓN
echo ========================================
echo.

cd /d "%~dp0"

echo 📍 Directorio del proyecto: %CD%
echo.

echo 🔍 Verificando requisitos del sistema...
echo.

REM Verificar Node.js
echo 1️⃣  Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js NO instalado
    echo 💡 Descarga desde: https://nodejs.org/
    set nodejs_ok=0
) else (
    echo ✅ Node.js instalado: 
    node --version
    set nodejs_ok=1
)
echo.

REM Verificar npm
echo 2️⃣  Verificando npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm NO disponible
    set npm_ok=0
) else (
    echo ✅ npm disponible: 
    npm --version
    set npm_ok=1
)
echo.

REM Verificar estructura del proyecto
echo 3️⃣  Verificando estructura del proyecto...

if exist "package.json" (
    echo ✅ package.json encontrado
    set frontend_ok=1
) else (
    echo ❌ package.json NO encontrado
    set frontend_ok=0
)

if exist "backend\simple-server.js" (
    echo ✅ Backend server encontrado
    set backend_ok=1
) else (
    echo ❌ Backend server NO encontrado
    set backend_ok=0
)

if exist "src" (
    echo ✅ Directorio src encontrado
    set src_ok=1
) else (
    echo ❌ Directorio src NO encontrado
    set src_ok=0
)

if exist "node_modules" (
    echo ✅ node_modules encontrado
    set modules_ok=1
) else (
    echo ⚠️  node_modules NO encontrado (se instalará automáticamente)
    set modules_ok=0
)
echo.

REM Verificar puertos
echo 4️⃣  Verificando puertos...

netstat -ano | findstr :3000 >nul 2>&1
if errorlevel 1 (
    echo ✅ Puerto 3000 disponible
    set port3000_ok=1
) else (
    echo ⚠️  Puerto 3000 en uso
    set port3000_ok=0
)

netstat -ano | findstr :5001 >nul 2>&1
if errorlevel 1 (
    echo ✅ Puerto 5001 disponible
    set port5001_ok=1
) else (
    echo ⚠️  Puerto 5001 en uso
    set port5001_ok=0
)
echo.

REM Resumen
echo ========================================
echo    📊 RESUMEN DE VERIFICACIÓN
echo ========================================
echo.

if %nodejs_ok%==1 if %npm_ok%==1 if %frontend_ok%==1 if %backend_ok%==1 if %src_ok%==1 (
    echo 🎉 ¡SISTEMA LISTO!
    echo.
    echo ✅ Todos los requisitos están cumplidos
    echo 🚀 Puedes ejecutar START-XUI-IMPORTER.bat
    echo.
    
    if %modules_ok%==0 (
        echo 📦 Las dependencias se instalarán automáticamente
        echo.
    )
    
    if %port3000_ok%==0 (
        echo ⚠️  Puerto 3000 en uso - se intentará usar otro puerto
    )
    
    if %port5001_ok%==0 (
        echo ⚠️  Puerto 5001 en uso - ejecuta STOP-XUI-IMPORTER.bat primero
    )
    
) else (
    echo ❌ SISTEMA NO LISTO
    echo.
    echo 🔧 Problemas encontrados:
    
    if %nodejs_ok%==0 echo    - Instalar Node.js
    if %npm_ok%==0 echo    - npm no disponible
    if %frontend_ok%==0 echo    - package.json faltante
    if %backend_ok%==0 echo    - Backend server faltante
    if %src_ok%==0 echo    - Directorio src faltante
    
    echo.
    echo 💡 Soluciona estos problemas antes de continuar
)

echo.
echo ========================================
pause
