@echo off
echo.
echo ========================================
echo    🎨 XUI IMPORTER - FRONTEND APP
echo ========================================
echo.

cd /d "%~dp0"

echo 📍 Directorio actual: %CD%
echo.

echo 🔍 Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js no está instalado o no está en el PATH
    echo 💡 Instala Node.js desde: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js encontrado: 
node --version
echo.

echo 🔍 Verificando npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm no está disponible
    pause
    exit /b 1
)

echo ✅ npm encontrado: 
npm --version
echo.

echo 🔍 Verificando package.json...
if not exist "package.json" (
    echo ❌ No se encontró package.json
    echo 📍 Asegúrate de estar en el directorio correcto del proyecto React
    pause
    exit /b 1
)

echo ✅ package.json encontrado
echo.

echo 🔍 Verificando node_modules...
if not exist "node_modules" (
    echo ⚠️  node_modules no encontrado
    echo 📦 Instalando dependencias...
    npm install
    if errorlevel 1 (
        echo ❌ Error instalando dependencias
        pause
        exit /b 1
    )
    echo ✅ Dependencias instaladas
    echo.
)

echo 🚀 Iniciando aplicación React...
echo 🌐 La aplicación estará disponible en: http://localhost:3000
echo 🛑 Presiona Ctrl+C para detener la aplicación
echo.
echo ========================================
echo.

npm start

echo.
echo 🛑 Aplicación detenida
pause
