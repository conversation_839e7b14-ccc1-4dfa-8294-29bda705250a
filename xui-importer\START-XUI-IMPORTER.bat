@echo off
title XUI IMPORTER - Iniciador Automático

echo.
echo ██╗  ██╗██╗   ██╗██╗    ██╗███╗   ███╗██████╗  ██████╗ ██████╗ ████████╗███████╗██████╗ 
echo ╚██╗██╔╝██║   ██║██║    ██║████╗ ████║██╔══██╗██╔═══██╗██╔══██╗╚══██╔══╝██╔════╝██╔══██╗
echo  ╚███╔╝ ██║   ██║██║    ██║██╔████╔██║██████╔╝██║   ██║██████╔╝   ██║   █████╗  ██████╔╝
echo  ██╔██╗ ██║   ██║██║    ██║██║╚██╔╝██║██╔═══╝ ██║   ██║██╔══██╗   ██║   ██╔══╝  ██╔══██╗
echo ██╔╝ ██╗╚██████╔╝██║    ██║██║ ╚═╝ ██║██║     ╚██████╔╝██║  ██║   ██║   ███████╗██║  ██║
echo ╚═╝  ╚═╝ ╚═════╝ ╚═╝    ╚═╝╚═╝     ╚═╝╚═╝      ╚═════╝ ╚═╝  ╚═╝   ╚═╝   ╚══════╝╚═╝  ╚═╝
echo.
echo ========================================
echo    🚀 INICIADOR AUTOMÁTICO
echo ========================================
echo.

cd /d "%~dp0"

echo 📍 Directorio del proyecto: %CD%
echo.

echo 🔍 Verificando estructura del proyecto...

if not exist "backend\simple-server.js" (
    echo ❌ No se encontró el backend (backend\simple-server.js)
    echo 📁 Estructura esperada:
    echo    📂 xui-importer\
    echo    ├── 📂 backend\
    echo    │   └── 📄 simple-server.js
    echo    ├── 📂 src\
    echo    └── 📄 package.json
    pause
    exit /b 1
)

if not exist "package.json" (
    echo ❌ No se encontró package.json del frontend
    pause
    exit /b 1
)

echo ✅ Estructura del proyecto verificada
echo.

echo 🚀 Iniciando XUI IMPORTER...
echo.
echo 📋 Se abrirán 2 ventanas:
echo    1️⃣  Backend Server (Puerto 5001)
echo    2️⃣  Frontend App (Puerto 3000)
echo.
echo ⚠️  NO CIERRES estas ventanas mientras uses la aplicación
echo.

echo 🔄 Iniciando Backend Server...
start "XUI IMPORTER - Backend Server" cmd /k "start-backend.bat"

echo ⏳ Esperando 3 segundos...
timeout /t 3 /nobreak >nul

echo 🔄 Iniciando Frontend App...
start "XUI IMPORTER - Frontend App" cmd /k "start-frontend.bat"

echo.
echo ✅ XUI IMPORTER iniciado correctamente!
echo.
echo 🌐 URLs de acceso:
echo    📊 Backend API: http://localhost:5001
echo    🎨 Frontend App: http://localhost:3000
echo.
echo 📋 Instrucciones:
echo    1. Espera a que ambas ventanas terminen de cargar
echo    2. El navegador se abrirá automáticamente en http://localhost:3000
echo    3. ¡Ya puedes usar XUI IMPORTER!
echo.
echo 🛑 Para detener la aplicación:
echo    - Cierra ambas ventanas del servidor
echo    - O presiona Ctrl+C en cada una
echo.

echo ⏳ Esperando 5 segundos antes de abrir el navegador...
timeout /t 5 /nobreak >nul

echo 🌐 Abriendo navegador...
start http://localhost:3000

echo.
echo 🎉 ¡XUI IMPORTER está listo para usar!
echo.
echo Presiona cualquier tecla para cerrar esta ventana...
pause >nul
