@echo off
title XUI IMPORTER - Desarrollo Desktop
cls

echo.
echo ========================================
echo     XUI IMPORTER - DESARROLLO
echo ========================================
echo.

cd /d "F:\WORKSPACE\XUI IMPORTER\xui-importer"

echo Limpiando procesos...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no instalado
    pause
    exit
)

echo Node.js OK
echo.

if not exist "node_modules" (
    echo Instalando dependencias...
    npm install
)

echo.
echo Iniciando en modo desarrollo...
echo (Mas rapido, con hot-reload)
echo.

REM Iniciar React en modo desarrollo
start "React Dev Server" cmd /c "npm start"

REM Esperar que React inicie
echo Esperando que React inicie...
timeout /t 10 /nobreak >nul

REM Iniciar Electron en modo desarrollo
echo Iniciando Electron...
set ELECTRON_IS_DEV=true
npm run electron

echo.
echo Aplicacion cerrada.
pause
