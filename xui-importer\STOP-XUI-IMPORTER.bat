@echo off
title XUI IMPORTER - Detener Servicios

echo.
echo ========================================
echo    🛑 XUI IMPORTER - DETENER SERVICIOS
echo ========================================
echo.

echo 🔍 Buscando procesos de Node.js...

echo.
echo 📋 Procesos Node.js activos:
tasklist /fi "imagename eq node.exe" 2>nul | find "node.exe"

if errorlevel 1 (
    echo ✅ No hay procesos de Node.js ejecutándose
    echo.
    goto :end
)

echo.
echo ⚠️  Se encontraron procesos de Node.js ejecutándose
echo.

:ask
echo 🤔 ¿Quieres detener TODOS los procesos de Node.js? (S/N)
set /p choice="Respuesta: "

if /i "%choice%"=="S" goto :kill
if /i "%choice%"=="Y" goto :kill
if /i "%choice%"=="N" goto :manual
if /i "%choice%"=="NO" goto :manual

echo ❌ Respuesta inválida. Usa S (Sí) o N (No)
goto :ask

:kill
echo.
echo 🛑 Deteniendo todos los procesos de Node.js...
taskkill /f /im node.exe >nul 2>&1

if errorlevel 1 (
    echo ⚠️  No se pudieron detener algunos procesos (pueden requerir permisos de administrador)
) else (
    echo ✅ Procesos de Node.js detenidos exitosamente
)

echo.
echo 🧹 Limpiando puertos...
echo    - Puerto 3000 (Frontend)
echo    - Puerto 5001 (Backend)

netstat -ano | findstr :3000 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  Puerto 3000 aún en uso
)

netstat -ano | findstr :5001 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  Puerto 5001 aún en uso
)

goto :end

:manual
echo.
echo 📋 Para detener manualmente:
echo    1. Ve a las ventanas del Backend y Frontend
echo    2. Presiona Ctrl+C en cada una
echo    3. O simplemente cierra las ventanas
echo.

:end
echo.
echo ✅ Proceso completado
echo.
echo 💡 Consejos:
echo    - Si los puertos siguen ocupados, reinicia el sistema
echo    - Para verificar puertos: netstat -ano ^| findstr :3000
echo.

pause
