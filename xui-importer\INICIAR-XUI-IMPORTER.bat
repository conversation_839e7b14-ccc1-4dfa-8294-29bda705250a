@echo off
setlocal enabledelayedexpansion
title XUI IMPORTER - Iniciando...
color 0A
cls

echo.
echo ========================================
echo        🚀 XUI IMPORTER v2.0
echo ========================================
echo.
echo 🔄 Iniciando aplicacion...
echo.

REM Ir al directorio del script
cd /d "%~dp0"
set PROJECT_DIR=%CD%

echo 📍 Directorio: %PROJECT_DIR%
echo.

REM Limpiar procesos anteriores
echo 🧹 Limpiando procesos anteriores...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im npm.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Verificar Node.js
echo 🔍 Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ ERROR: Node.js no esta instalado
    echo.
    echo 💡 SOLUCION:
    echo    1. Ve a: https://nodejs.org/
    echo    2. Descarga la version LTS
    echo    3. Instala y reinicia el sistema
    echo    4. Ejecuta este archivo nuevamente
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% encontrado
echo.

REM Verificar estructura del proyecto
echo 🔍 Verificando archivos del proyecto...

if not exist "package.json" (
    echo ❌ ERROR: package.json no encontrado
    echo 📍 Asegurate de ejecutar este archivo desde la carpeta del proyecto
    pause
    exit /b 1
)

if not exist "backend\simple-server.js" (
    echo ❌ ERROR: Backend no encontrado
    echo 📍 Falta: backend\simple-server.js
    pause
    exit /b 1
)

if not exist "src" (
    echo ❌ ERROR: Carpeta src no encontrada
    echo 📍 Estructura del proyecto incorrecta
    pause
    exit /b 1
)

echo ✅ Estructura del proyecto correcta
echo.

REM Verificar/Instalar dependencias
if not exist "node_modules" (
    echo 📦 Instalando dependencias por primera vez...
    echo    (Esto puede tomar varios minutos)
    echo.
    npm install --silent
    if errorlevel 1 (
        echo ❌ ERROR: No se pudieron instalar las dependencias
        echo 💡 Verifica tu conexion a internet
        pause
        exit /b 1
    )
    echo ✅ Dependencias instaladas correctamente
    echo.
) else (
    echo ✅ Dependencias ya instaladas
    echo.
)

REM Verificar puertos disponibles
echo 🔍 Verificando puertos...
netstat -ano | findstr :5001 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  Puerto 5001 ocupado, liberando...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5001') do (
        taskkill /f /pid %%a >nul 2>&1
    )
    timeout /t 2 /nobreak >nul
)

netstat -ano | findstr :3000 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  Puerto 3000 ocupado, liberando...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
        taskkill /f /pid %%a >nul 2>&1
    )
    timeout /t 2 /nobreak >nul
)

echo ✅ Puertos disponibles
echo.

echo ========================================
echo        🚀 INICIANDO SERVIDORES
echo ========================================
echo.

REM Crear script temporal para backend
echo @echo off > temp_backend.bat
echo cd /d "%PROJECT_DIR%\backend" >> temp_backend.bat
echo title XUI-Backend-Server >> temp_backend.bat
echo echo Backend iniciado en puerto 5001 >> temp_backend.bat
echo node simple-server.js >> temp_backend.bat
echo pause >> temp_backend.bat

REM Crear script temporal para frontend
echo @echo off > temp_frontend.bat
echo cd /d "%PROJECT_DIR%" >> temp_frontend.bat
echo title XUI-Frontend-App >> temp_frontend.bat
echo echo Frontend iniciado en puerto 3000 >> temp_frontend.bat
echo set BROWSER=none >> temp_frontend.bat
echo npm start >> temp_frontend.bat
echo pause >> temp_frontend.bat

REM Iniciar Backend
echo 🔧 Iniciando Backend Server...
start "XUI-Backend" temp_backend.bat

REM Esperar un poco
echo ⏳ Esperando 3 segundos...
timeout /t 3 /nobreak >nul

REM Iniciar Frontend
echo 🎨 Iniciando Frontend App...
start "XUI-Frontend" temp_frontend.bat

echo.
echo ⏳ Esperando que los servidores inicien completamente...
echo    (Esto puede tomar 15-30 segundos)
echo.

REM Esperar tiempo suficiente
timeout /t 15 /nobreak >nul

REM Verificar que los procesos esten corriendo
tasklist | findstr node.exe >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Los servidores no se iniciaron correctamente
    echo 💡 Revisa las ventanas del backend y frontend para ver errores
    pause
    exit /b 1
)

echo ✅ Servidores iniciados correctamente
echo.

REM Limpiar archivos temporales
del temp_backend.bat >nul 2>&1
del temp_frontend.bat >nul 2>&1

echo 🌐 Abriendo navegador...
timeout /t 2 /nobreak >nul
start http://localhost:3000

cls
echo.
echo ========================================
echo     ✅ XUI IMPORTER LISTO PARA USAR
echo ========================================
echo.
echo 🌐 URL: http://localhost:3000
echo 🔧 Backend: http://localhost:5001
echo.
echo 📋 ESTADO:
echo    ✅ Backend Server: ACTIVO
echo    ✅ Frontend App: ACTIVO  
echo    ✅ Navegador: ABIERTO
echo.
echo 💡 INSTRUCCIONES:
echo    1. La aplicacion se abrio en tu navegador
echo    2. Si no se abrio, ve manualmente a: http://localhost:3000
echo    3. Puedes importar tus archivos M3U
echo    4. Las ventanas del backend y frontend deben permanecer abiertas
echo.
echo 🛑 PARA DETENER:
echo    - Cierra las ventanas del Backend y Frontend
echo    - O presiona cualquier tecla aqui para cerrar todo
echo.
echo ========================================

pause >nul

echo.
echo 🛑 Cerrando aplicacion...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im npm.exe >nul 2>&1
echo ✅ Aplicacion cerrada

echo.
echo 👋 Gracias por usar XUI IMPORTER
timeout /t 3 /nobreak >nul
exit
