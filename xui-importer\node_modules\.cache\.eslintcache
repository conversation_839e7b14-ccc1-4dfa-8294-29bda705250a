[{"F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\index.js": "1", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\reportWebVitals.js": "2", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\App.js": "3", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\NavigationBar.js": "4", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Dashboard.js": "5", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ImportM3U.js": "6", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Sidebar.js": "7", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Profile.js": "8", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\History.js": "9", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Connections.js": "10", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DebugPanel.js": "11", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\SeriesImportHandler.js": "12", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\seriesLogic.js": "13", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\services\\apiService.js": "14", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\BackendStatus.js": "15", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\context\\AppContext.js": "16", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingProgress.js": "17", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingModal.js": "18", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DatabaseOptimization.js": "19", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\hooks\\useDatabaseWorker.js": "20", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\workers\\databaseWorker.js": "21", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\config\\apiConfig.js": "22", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ConnectivityDiagnostic.js": "23", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\connectivityTest.js": "24"}, {"size": 582, "mtime": 1752616067662, "results": "25", "hashOfConfig": "26"}, {"size": 362, "mtime": 1752612862467, "results": "27", "hashOfConfig": "26"}, {"size": 1621, "mtime": 1752763875096, "results": "28", "hashOfConfig": "26"}, {"size": 878, "mtime": 1752613257084, "results": "29", "hashOfConfig": "26"}, {"size": 11095, "mtime": 1752729154482, "results": "30", "hashOfConfig": "26"}, {"size": 35428, "mtime": 1752854397111, "results": "31", "hashOfConfig": "26"}, {"size": 994, "mtime": 1752763845115, "results": "32", "hashOfConfig": "26"}, {"size": 20767, "mtime": 1752620222163, "results": "33", "hashOfConfig": "26"}, {"size": 12297, "mtime": 1752703292672, "results": "34", "hashOfConfig": "26"}, {"size": 19905, "mtime": 1752708600731, "results": "35", "hashOfConfig": "26"}, {"size": 3561, "mtime": 1752620222163, "results": "36", "hashOfConfig": "26"}, {"size": 11480, "mtime": 1752854110462, "results": "37", "hashOfConfig": "26"}, {"size": 16014, "mtime": 1752721373152, "results": "38", "hashOfConfig": "26"}, {"size": 14296, "mtime": 1752789632883, "results": "39", "hashOfConfig": "26"}, {"size": 1617, "mtime": 1752627612546, "results": "40", "hashOfConfig": "26"}, {"size": 19465, "mtime": 1752684469144, "results": "41", "hashOfConfig": "26"}, {"size": 6900, "mtime": 1752685510779, "results": "42", "hashOfConfig": "26"}, {"size": 9257, "mtime": 1752685535428, "results": "43", "hashOfConfig": "26"}, {"size": 7557, "mtime": 1752703292686, "results": "44", "hashOfConfig": "26"}, {"size": 6043, "mtime": 1752701323984, "results": "45", "hashOfConfig": "26"}, {"size": 9869, "mtime": 1752703445206, "results": "46", "hashOfConfig": "26"}, {"size": 4534, "mtime": 1752765805111, "results": "47", "hashOfConfig": "26"}, {"size": 8830, "mtime": 1752703292740, "results": "48", "hashOfConfig": "26"}, {"size": 5494, "mtime": 1752686091588, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "z8i460", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\index.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\reportWebVitals.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\App.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\NavigationBar.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Dashboard.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ImportM3U.js", [], ["122", "123"], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Sidebar.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Profile.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\History.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Connections.js", [], ["124"], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DebugPanel.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\SeriesImportHandler.js", ["125", "126"], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\seriesLogic.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\services\\apiService.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\BackendStatus.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\context\\AppContext.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingProgress.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingModal.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DatabaseOptimization.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\hooks\\useDatabaseWorker.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\workers\\databaseWorker.js", [], ["127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142"], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\config\\apiConfig.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ConnectivityDiagnostic.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\connectivityTest.js", [], [], {"ruleId": "143", "severity": 1, "message": "144", "line": 313, "column": 6, "nodeType": "145", "endLine": 313, "endColumn": 8, "suggestions": "146", "suppressions": "147"}, {"ruleId": "143", "severity": 1, "message": "148", "line": 321, "column": 6, "nodeType": "145", "endLine": 321, "endColumn": 21, "suggestions": "149", "suppressions": "150"}, {"ruleId": "143", "severity": 1, "message": "151", "line": 41, "column": 6, "nodeType": "145", "endLine": 41, "endColumn": 32, "suggestions": "152", "suppressions": "153"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 3, "column": 29, "nodeType": "156", "messageId": "157", "endLine": 3, "endColumn": 52}, {"ruleId": "143", "severity": 1, "message": "158", "line": 18, "column": 6, "nodeType": "145", "endLine": 18, "endColumn": 20, "suggestions": "159"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 108, "column": 5, "nodeType": "156", "messageId": "162", "endLine": 108, "endColumn": 9, "suppressions": "163"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 114, "column": 5, "nodeType": "156", "messageId": "162", "endLine": 114, "endColumn": 9, "suppressions": "164"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 124, "column": 5, "nodeType": "156", "messageId": "162", "endLine": 124, "endColumn": 9, "suppressions": "165"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 135, "column": 7, "nodeType": "156", "messageId": "162", "endLine": 135, "endColumn": 11, "suppressions": "166"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 144, "column": 7, "nodeType": "156", "messageId": "162", "endLine": 144, "endColumn": 11, "suppressions": "167"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 155, "column": 5, "nodeType": "156", "messageId": "162", "endLine": 155, "endColumn": 9, "suppressions": "168"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 177, "column": 5, "nodeType": "156", "messageId": "162", "endLine": 177, "endColumn": 9, "suppressions": "169"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 188, "column": 5, "nodeType": "156", "messageId": "162", "endLine": 188, "endColumn": 9, "suppressions": "170"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 213, "column": 9, "nodeType": "156", "messageId": "162", "endLine": 213, "endColumn": 13, "suppressions": "171"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 231, "column": 5, "nodeType": "156", "messageId": "162", "endLine": 231, "endColumn": 9, "suppressions": "172"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 302, "column": 7, "nodeType": "156", "messageId": "162", "endLine": 302, "endColumn": 11, "suppressions": "173"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 333, "column": 5, "nodeType": "156", "messageId": "162", "endLine": 333, "endColumn": 9, "suppressions": "174"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 346, "column": 5, "nodeType": "156", "messageId": "162", "endLine": 346, "endColumn": 9, "suppressions": "175"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 359, "column": 1, "nodeType": "156", "messageId": "162", "endLine": 359, "endColumn": 5, "suppressions": "176"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 367, "column": 9, "nodeType": "156", "messageId": "162", "endLine": 367, "endColumn": 13, "suppressions": "177"}, {"ruleId": "160", "severity": 2, "message": "161", "line": 377, "column": 7, "nodeType": "156", "messageId": "162", "endLine": 377, "endColumn": 11, "suppressions": "178"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkBackendStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["179"], ["180"], "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["181"], ["182"], "React Hook useEffect has a missing dependency: 'updateConnectionStatus'. Either include it or remove the dependency array.", ["183"], ["184"], "no-unused-vars", "'validateSeriesStructure' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'analyzeM3UFile'. Either include it or remove the dependency array.", ["185"], "no-restricted-globals", "Unexpected use of 'self'.", "defaultMessage", ["186"], ["187"], ["188"], ["189"], ["190"], ["191"], ["192"], ["193"], ["194"], ["195"], ["196"], ["197"], ["198"], ["199"], ["200"], ["201"], {"desc": "202", "fix": "203"}, {"kind": "204", "justification": "205"}, {"desc": "206", "fix": "207"}, {"kind": "204", "justification": "205"}, {"desc": "208", "fix": "209"}, {"kind": "204", "justification": "205"}, {"desc": "210", "fix": "211"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, {"kind": "204", "justification": "205"}, "Update the dependencies array to be: [checkBackendStatus]", {"range": "212", "text": "213"}, "directive", "", "Update the dependencies array to be: [backendStatus, loadInitialData]", {"range": "214", "text": "215"}, "Update the dependencies array to be: [state.databaseConnection, updateConnectionStatus]", {"range": "216", "text": "217"}, "Update the dependencies array to be: [analyzeM3UFile, selectedFile]", {"range": "218", "text": "219"}, [11288, 11290], "[checkBackendStatus]", [11508, 11523], "[backendStatus, loadInitialData]", [1417, 1443], "[state.databaseConnection, updateConnectionStatus]", [864, 878], "[analyzeM3UFile, selectedFile]"]