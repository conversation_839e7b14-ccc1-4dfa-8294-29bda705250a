[{"F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\index.js": "1", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\reportWebVitals.js": "2", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\App.js": "3", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\NavigationBar.js": "4", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Dashboard.js": "5", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ImportM3U.js": "6", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Sidebar.js": "7", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Profile.js": "8", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\History.js": "9", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Connections.js": "10", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DebugPanel.js": "11", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\SeriesImportHandler.js": "12", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\seriesLogic.js": "13", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\services\\apiService.js": "14", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\BackendStatus.js": "15", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\context\\AppContext.js": "16", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingProgress.js": "17", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingModal.js": "18", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DatabaseOptimization.js": "19", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\hooks\\useDatabaseWorker.js": "20", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\workers\\databaseWorker.js": "21", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\config\\apiConfig.js": "22", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ConnectivityDiagnostic.js": "23", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\connectivityTest.js": "24", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\CategorySelectorTest.js": "25", "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\CategorySelector.jsx": "26"}, {"size": 582, "mtime": 1752616067662, "results": "27", "hashOfConfig": "28"}, {"size": 362, "mtime": 1752612862467, "results": "29", "hashOfConfig": "28"}, {"size": 1621, "mtime": 1752763875096, "results": "30", "hashOfConfig": "28"}, {"size": 878, "mtime": 1752613257084, "results": "31", "hashOfConfig": "28"}, {"size": 11095, "mtime": 1752729154482, "results": "32", "hashOfConfig": "28"}, {"size": 35414, "mtime": 1752786251522, "results": "33", "hashOfConfig": "28"}, {"size": 994, "mtime": 1752763845115, "results": "34", "hashOfConfig": "28"}, {"size": 20767, "mtime": 1752620222163, "results": "35", "hashOfConfig": "28"}, {"size": 12297, "mtime": 1752703292672, "results": "36", "hashOfConfig": "28"}, {"size": 19905, "mtime": 1752708600731, "results": "37", "hashOfConfig": "28"}, {"size": 3561, "mtime": 1752620222163, "results": "38", "hashOfConfig": "28"}, {"size": 11910, "mtime": 1752703292672, "results": "39", "hashOfConfig": "28"}, {"size": 16014, "mtime": 1752721373152, "results": "40", "hashOfConfig": "28"}, {"size": 14346, "mtime": 1752784383007, "results": "41", "hashOfConfig": "28"}, {"size": 1617, "mtime": 1752627612546, "results": "42", "hashOfConfig": "28"}, {"size": 19465, "mtime": 1752684469144, "results": "43", "hashOfConfig": "28"}, {"size": 6900, "mtime": 1752685510779, "results": "44", "hashOfConfig": "28"}, {"size": 9257, "mtime": 1752685535428, "results": "45", "hashOfConfig": "28"}, {"size": 7557, "mtime": 1752703292686, "results": "46", "hashOfConfig": "28"}, {"size": 6043, "mtime": 1752701323984, "results": "47", "hashOfConfig": "28"}, {"size": 9869, "mtime": 1752703445206, "results": "48", "hashOfConfig": "28"}, {"size": 4534, "mtime": 1752765805111, "results": "49", "hashOfConfig": "28"}, {"size": 8830, "mtime": 1752703292740, "results": "50", "hashOfConfig": "28"}, {"size": 5494, "mtime": 1752686091588, "results": "51", "hashOfConfig": "28"}, {"size": 2371, "mtime": 1752785640609, "results": "52", "hashOfConfig": "28"}, {"size": 7435, "mtime": 1752786277603, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "z8i460", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\index.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\reportWebVitals.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\App.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\NavigationBar.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Dashboard.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ImportM3U.js", [], ["132", "133"], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Sidebar.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Profile.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\History.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\Connections.js", [], ["134"], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DebugPanel.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\SeriesImportHandler.js", ["135", "136", "137"], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\seriesLogic.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\services\\apiService.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\BackendStatus.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\context\\AppContext.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingProgress.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DataReadingModal.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\DatabaseOptimization.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\hooks\\useDatabaseWorker.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\workers\\databaseWorker.js", [], ["138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153"], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\config\\apiConfig.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\ConnectivityDiagnostic.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\utils\\connectivityTest.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\CategorySelectorTest.js", [], [], "F:\\WORKSPACE\\XUI IMPORTER\\xui-importer\\src\\components\\CategorySelector.jsx", ["154", "155", "156"], [], {"ruleId": "157", "severity": 1, "message": "158", "line": 316, "column": 6, "nodeType": "159", "endLine": 316, "endColumn": 8, "suggestions": "160", "suppressions": "161"}, {"ruleId": "157", "severity": 1, "message": "162", "line": 324, "column": 6, "nodeType": "159", "endLine": 324, "endColumn": 21, "suggestions": "163", "suppressions": "164"}, {"ruleId": "157", "severity": 1, "message": "165", "line": 41, "column": 6, "nodeType": "159", "endLine": 41, "endColumn": 32, "suggestions": "166", "suppressions": "167"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 3, "column": 54, "nodeType": "170", "messageId": "171", "endLine": 3, "endColumn": 80}, {"ruleId": "157", "severity": 1, "message": "172", "line": 18, "column": 6, "nodeType": "159", "endLine": 18, "endColumn": 20, "suggestions": "173"}, {"ruleId": "168", "severity": 1, "message": "174", "line": 107, "column": 9, "nodeType": "170", "messageId": "171", "endLine": 107, "endColumn": 33}, {"ruleId": "175", "severity": 2, "message": "176", "line": 108, "column": 5, "nodeType": "170", "messageId": "177", "endLine": 108, "endColumn": 9, "suppressions": "178"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 114, "column": 5, "nodeType": "170", "messageId": "177", "endLine": 114, "endColumn": 9, "suppressions": "179"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 124, "column": 5, "nodeType": "170", "messageId": "177", "endLine": 124, "endColumn": 9, "suppressions": "180"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 135, "column": 7, "nodeType": "170", "messageId": "177", "endLine": 135, "endColumn": 11, "suppressions": "181"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 144, "column": 7, "nodeType": "170", "messageId": "177", "endLine": 144, "endColumn": 11, "suppressions": "182"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 155, "column": 5, "nodeType": "170", "messageId": "177", "endLine": 155, "endColumn": 9, "suppressions": "183"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 177, "column": 5, "nodeType": "170", "messageId": "177", "endLine": 177, "endColumn": 9, "suppressions": "184"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 188, "column": 5, "nodeType": "170", "messageId": "177", "endLine": 188, "endColumn": 9, "suppressions": "185"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 213, "column": 9, "nodeType": "170", "messageId": "177", "endLine": 213, "endColumn": 13, "suppressions": "186"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 231, "column": 5, "nodeType": "170", "messageId": "177", "endLine": 231, "endColumn": 9, "suppressions": "187"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 302, "column": 7, "nodeType": "170", "messageId": "177", "endLine": 302, "endColumn": 11, "suppressions": "188"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 333, "column": 5, "nodeType": "170", "messageId": "177", "endLine": 333, "endColumn": 9, "suppressions": "189"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 346, "column": 5, "nodeType": "170", "messageId": "177", "endLine": 346, "endColumn": 9, "suppressions": "190"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 359, "column": 1, "nodeType": "170", "messageId": "177", "endLine": 359, "endColumn": 5, "suppressions": "191"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 367, "column": 9, "nodeType": "170", "messageId": "177", "endLine": 367, "endColumn": 13, "suppressions": "192"}, {"ruleId": "175", "severity": 2, "message": "176", "line": 377, "column": 7, "nodeType": "170", "messageId": "177", "endLine": 377, "endColumn": 11, "suppressions": "193"}, {"ruleId": "157", "severity": 1, "message": "194", "line": 47, "column": 6, "nodeType": "159", "endLine": 47, "endColumn": 36, "suggestions": "195"}, {"ruleId": "196", "severity": 1, "message": "197", "line": 187, "column": 109, "nodeType": "198", "messageId": "199", "endLine": 187, "endColumn": 111}, {"ruleId": "196", "severity": 1, "message": "197", "line": 197, "column": 65, "nodeType": "198", "messageId": "199", "endLine": 197, "endColumn": 67}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkBackendStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["200"], ["201"], "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["202"], ["203"], "React Hook useEffect has a missing dependency: 'updateConnectionStatus'. Either include it or remove the dependency array.", ["204"], ["205"], "no-unused-vars", "'generateEpisodeDisplayName' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'analyzeM3UFile'. Either include it or remove the dependency array.", ["206"], "'validateAndPrepareImport' is assigned a value but never used.", "no-restricted-globals", "Unexpected use of 'self'.", "defaultMessage", ["207"], ["208"], ["209"], ["210"], ["211"], ["212"], ["213"], ["214"], ["215"], ["216"], ["217"], ["218"], ["219"], ["220"], ["221"], ["222"], "React Hook useEffect has missing dependencies: 'loadBouquetCategories' and 'loadCategoriesByType'. Either include them or remove the dependency array.", ["223"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", {"desc": "224", "fix": "225"}, {"kind": "226", "justification": "227"}, {"desc": "228", "fix": "229"}, {"kind": "226", "justification": "227"}, {"desc": "230", "fix": "231"}, {"kind": "226", "justification": "227"}, {"desc": "232", "fix": "233"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"desc": "234", "fix": "235"}, "Update the dependencies array to be: [checkBackendStatus]", {"range": "236", "text": "237"}, "directive", "", "Update the dependencies array to be: [backendStatus, loadInitialData]", {"range": "238", "text": "239"}, "Update the dependencies array to be: [state.databaseConnection, updateConnectionStatus]", {"range": "240", "text": "241"}, "Update the dependencies array to be: [analyzeM3UFile, selectedFile]", {"range": "242", "text": "243"}, "Update the dependencies array to be: [contentType, loadBouquetCategories, loadCategoriesByType, selectedBouquet]", {"range": "244", "text": "245"}, [11547, 11549], "[checkBackendStatus]", [11767, 11782], "[backendStatus, loadInitialData]", [1417, 1443], "[state.databaseConnection, updateConnectionStatus]", [892, 906], "[analyzeM3UFile, selectedFile]", [1536, 1566], "[contentType, loadBouquetCategories, loadCategoriesByType, selectedBouquet]"]