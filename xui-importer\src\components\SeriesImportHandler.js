import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Form, Button, Alert, Table, Badge, Modal } from 'react-bootstrap';
import { parseM3UForSeries, validateSeriesStructure } from '../utils/seriesLogic';

const SeriesImportHandler = ({ selectedFile, isImporting, onSeriesDetected }) => {
  const [detectedSeries, setDetectedSeries] = useState([]);
  const [selectedSeries, setSelectedSeries] = useState(new Set());
  const [showPreview, setShowPreview] = useState(false);
  const [previewSeries, setPreviewSeries] = useState(null);
  const [tmdbMatching, setTmdbMatching] = useState({});
  const [seriesSettings, setSeriesSettings] = useState({});

  // Analizar archivo M3U cuando se selecciona
  useEffect(() => {
    if (selectedFile && selectedFile.type === 'application/x-mpegurl') {
      analyzeM3UFile();
    }
  }, [selectedFile]); // analyzeM3UFile se define dentro del componente, no necesita estar en dependencias

  const analyzeM3UFile = async () => {
    window.debugLog('info', '🔍 Analizando archivo M3U para series...');
    
    try {
      const fileContent = await selectedFile.text();
      const series = parseM3UForSeries(fileContent);
      
      setDetectedSeries(series);
      
      // Inicializar configuraciones por defecto
      const defaultSettings = {};
      series.forEach(s => {
        defaultSettings[s.title] = {
          category_id: [],
          tmdb_id: null,
          auto_fetch_metadata: true,
          merge_similar: false
        };
      });
      setSeriesSettings(defaultSettings);
      
      window.debugLog('success', `✅ Detectadas ${series.length} series en el archivo`);
      
      // Notificar al componente padre
      if (onSeriesDetected) {
        onSeriesDetected(series);
      }
      
    } catch (error) {
      window.debugLog('error', `❌ Error analizando M3U: ${error.message}`);
    }
  };

  const handleSeriesSelection = (seriesTitle, isSelected) => {
    const newSelection = new Set(selectedSeries);
    if (isSelected) {
      newSelection.add(seriesTitle);
    } else {
      newSelection.delete(seriesTitle);
    }
    setSelectedSeries(newSelection);
  };

  const handleSelectAll = () => {
    if (selectedSeries.size === detectedSeries.length) {
      setSelectedSeries(new Set());
    } else {
      setSelectedSeries(new Set(detectedSeries.map(s => s.title)));
    }
  };

  const showSeriesPreview = (series) => {
    setPreviewSeries(series);
    setShowPreview(true);
  };

  const updateSeriesSettings = (seriesTitle, setting, value) => {
    setSeriesSettings(prev => ({
      ...prev,
      [seriesTitle]: {
        ...prev[seriesTitle],
        [setting]: value
      }
    }));
  };

  const searchTMDB = async (seriesTitle) => {
    window.debugLog('info', `🔍 Buscando "${seriesTitle}" en TMDB...`);
    
    // Mock de búsqueda TMDB - aquí iría la integración real
    setTimeout(() => {
      setTmdbMatching(prev => ({
        ...prev,
        [seriesTitle]: {
          found: true,
          tmdb_id: Math.floor(Math.random() * 100000),
          title: seriesTitle,
          overview: `Serie detectada: ${seriesTitle}`,
          poster_path: '/mock-poster.jpg',
          first_air_date: '2023-01-01',
          vote_average: 8.5
        }
      }));
      window.debugLog('success', `✅ Metadata encontrada para "${seriesTitle}"`);
    }, 1000);
  };

  // Función validateAndPrepareImport eliminada - no se usaba

  if (!selectedFile || detectedSeries.length === 0) {
    return (
      <Alert variant="info">
        📺 Selecciona un archivo M3U para detectar series automáticamente
      </Alert>
    );
  }

  return (
    <Card className="mt-3">
      <Card.Header className="d-flex justify-content-between align-items-center">
        <h6 className="mb-0">📺 Series Detectadas ({detectedSeries.length})</h6>
        <div>
          <Button
            variant="outline-primary"
            size="sm"
            onClick={handleSelectAll}
            className="me-2"
          >
            {selectedSeries.size === detectedSeries.length ? 'Deseleccionar Todo' : 'Seleccionar Todo'}
          </Button>
          <Badge bg="info">{selectedSeries.size} seleccionadas</Badge>
        </div>
      </Card.Header>
      
      <Card.Body style={{ maxHeight: '400px', overflowY: 'auto' }}>
        {detectedSeries.map((series, index) => (
          <Card key={`series-${series.title}-${index}`} className="mb-3 border">
            <Card.Body className="py-2">
              <Row className="align-items-center">
                <Col md={1}>
                  <Form.Check
                    type="checkbox"
                    checked={selectedSeries.has(series.title)}
                    onChange={(e) => handleSeriesSelection(series.title, e.target.checked)}
                    disabled={isImporting}
                  />
                </Col>
                
                <Col md={4}>
                  <strong>{series.title}</strong>
                  <br />
                  <small className="text-muted">
                    {series.episodes.length} episodios
                  </small>
                </Col>
                
                <Col md={2}>
                  <div className="d-flex flex-wrap gap-1">
                    {[...new Set(series.episodes.map(ep => `S${ep.season_num}`))].map(season => (
                      <Badge key={season} bg="secondary" style={{ fontSize: '0.7em' }}>
                        {season}
                      </Badge>
                    ))}
                  </div>
                </Col>
                
                <Col md={2}>
                  {tmdbMatching[series.title] ? (
                    <Badge bg="success">✅ TMDB</Badge>
                  ) : (
                    <Button
                      variant="outline-info"
                      size="sm"
                      onClick={() => searchTMDB(series.title)}
                      disabled={isImporting}
                    >
                      🔍 TMDB
                    </Button>
                  )}
                </Col>
                
                <Col md={3}>
                  <div className="d-flex gap-1">
                    <Button
                      variant="outline-primary"
                      size="sm"
                      onClick={() => showSeriesPreview(series)}
                    >
                      👁️ Ver
                    </Button>
                    
                    <Form.Select
                      size="sm"
                      value={seriesSettings[series.title]?.category_id?.[0] || ''}
                      onChange={(e) => updateSeriesSettings(series.title, 'category_id', [parseInt(e.target.value)])}
                      disabled={isImporting}
                    >
                      <option value="">Categoría...</option>
                      <option value="1">Drama Series</option>
                      <option value="2">Comedy Series</option>
                      <option value="3">Action Series</option>
                      <option value="4">Sci-Fi Series</option>
                    </Form.Select>
                  </div>
                </Col>
              </Row>
              
              {tmdbMatching[series.title] && (
                <Row className="mt-2">
                  <Col>
                    <Alert variant="success" className="py-1 mb-0">
                      <small>
                        <strong>TMDB:</strong> {tmdbMatching[series.title].title} 
                        ({tmdbMatching[series.title].first_air_date?.split('-')[0]}) - 
                        ⭐ {tmdbMatching[series.title].vote_average}
                      </small>
                    </Alert>
                  </Col>
                </Row>
              )}
            </Card.Body>
          </Card>
        ))}
      </Card.Body>
      
      {selectedSeries.size > 0 && (
        <Card.Footer>
          <Alert variant="success" className="mb-2">
            ✅ {selectedSeries.size} series seleccionadas para importar
          </Alert>
          
          <Form.Group className="mb-2">
            <Form.Label>⚙️ Configuración Global para Series Seleccionadas</Form.Label>
            <Row>
              <Col md={4}>
                <Form.Check 
                  type="checkbox" 
                  label="🔍 Auto-buscar metadata en TMDB"
                  defaultChecked={true}
                />
              </Col>
              <Col md={4}>
                <Form.Check 
                  type="checkbox" 
                  label="🔗 Merge episodios similares"
                  defaultChecked={false}
                />
              </Col>
              <Col md={4}>
                <Form.Check 
                  type="checkbox" 
                  label="📝 Generar descripciones automáticas"
                  defaultChecked={true}
                />
              </Col>
            </Row>
          </Form.Group>
        </Card.Footer>
      )}

      {/* Modal de Preview */}
      <Modal show={showPreview} onHide={() => setShowPreview(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>📺 Preview: {previewSeries?.title}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {previewSeries && (
            <>
              <Row className="mb-3">
                <Col>
                  <strong>Total de Episodios:</strong> {previewSeries.episodes.length}
                  <br />
                  <strong>Temporadas:</strong> {[...new Set(previewSeries.episodes.map(ep => ep.season_num))].join(', ')}
                </Col>
              </Row>
              
              <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                <Table striped size="sm">
                  <thead>
                    <tr>
                      <th>Episodio</th>
                      <th>Nombre</th>
                      <th>Duración</th>
                    </tr>
                  </thead>
                  <tbody>
                    {previewSeries.episodes
                      .sort((a, b) => a.season_num - b.season_num || a.episode_num - b.episode_num)
                      .map((episode, index) => (
                      <tr key={`episode-S${episode.season_num}E${episode.episode_num}-${index}`}>
                        <td>
                          <Badge bg="primary">
                            S{String(episode.season_num).padStart(2, '0')}E{String(episode.episode_num).padStart(2, '0')}
                          </Badge>
                        </td>
                        <td>{episode.episode_title || 'Sin título'}</td>
                        <td>{episode.duration}</td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPreview(false)}>
            Cerrar
          </Button>
        </Modal.Footer>
      </Modal>
    </Card>
  );
};

export default SeriesImportHandler;
