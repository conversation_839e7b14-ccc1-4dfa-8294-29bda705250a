@echo off
title XUI IMPORTER
cls

echo.
echo ================================
echo     XUI IMPORTER - INICIANDO
echo ================================
echo.

REM Ir al directorio del proyecto React
cd /d "F:\WORKSPACE\XUI IMPORTER\xui-importer"

echo Directorio actual: %CD%
echo.

REM Matar procesos anteriores
echo Limpiando procesos anteriores...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Verificar Node.js
echo Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js no instalado
    echo Instala Node.js desde: https://nodejs.org/
    pause
    exit
)

echo Node.js OK
echo.

REM Instalar dependencias si no existen
if not exist "node_modules" (
    echo Instalando dependencias...
    npm install
    echo.
)

echo Iniciando Backend...
start "Backend" cmd /c "cd backend && echo Backend iniciado && node simple-server.js && pause"

echo Esperando 5 segundos...
timeout /t 5 /nobreak >nul

echo Iniciando Frontend...
start "Frontend" cmd /c "echo Frontend iniciado && npm start && pause"

echo Esperando 15 segundos para que todo cargue...
timeout /t 15 /nobreak >nul

echo.
echo Abriendo navegador...
start http://localhost:3000

echo.
echo ================================
echo     APLICACION INICIADA
echo ================================
echo.
echo Frontend: http://localhost:3000
echo Backend:  http://localhost:5001
echo.
echo IMPORTANTE:
echo - NO cierres las ventanas del Backend y Frontend
echo - Si algo no funciona, revisa esas ventanas
echo.
echo Presiona cualquier tecla para cerrar todo...
pause >nul

echo.
echo Cerrando aplicacion...
taskkill /f /im node.exe >nul 2>&1
echo Aplicacion cerrada.
timeout /t 2 /nobreak >nul
