@echo off
title XUI IMPORTER - Iniciando...
color 0A

echo.
echo ██╗  ██╗██╗   ██╗██╗    ██╗███╗   ███╗██████╗  ██████╗ ██████╗ ████████╗███████╗██████╗ 
echo ╚██╗██╔╝██║   ██║██║    ██║████╗ ████║██╔══██╗██╔═══██╗██╔══██╗╚══██╔══╝██╔════╝██╔══██╗
echo  ╚███╔╝ ██║   ██║██║    ██║██╔████╔██║██████╔╝██║   ██║██████╔╝   ██║   █████╗  ██████╔╝
echo  ██╔██╗ ██║   ██║██║    ██║██║╚██╔╝██║██╔═══╝ ██║   ██║██╔══██╗   ██║   ██╔══╝  ██╔══██╗
echo ██╔╝ ██╗╚██████╔╝██║    ██║██║ ╚═╝ ██║██║     ╚██████╔╝██║  ██║   ██║   ███████╗██║  ██║
echo ╚═╝  ╚═╝ ╚═════╝ ╚═╝    ╚═╝╚═╝     ╚═╝╚═╝      ╚═════╝ ╚═╝  ╚═╝   ╚═╝   ╚══════╝╚═╝  ╚═╝
echo.
echo                           🚀 INICIANDO AUTOMATICAMENTE...
echo.

cd /d "%~dp0"

REM Matar procesos anteriores
echo 🧹 Limpiando procesos anteriores...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Verificar Node.js
echo 🔍 Verificando Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js no encontrado
    echo 💡 Instala Node.js desde: https://nodejs.org/
    echo.
    pause
    exit /b 1
)
echo ✅ Node.js OK

REM Verificar estructura
echo 🔍 Verificando archivos...
if not exist "backend\simple-server.js" (
    echo ❌ Backend no encontrado
    pause
    exit /b 1
)
if not exist "package.json" (
    echo ❌ Frontend no encontrado  
    pause
    exit /b 1
)
echo ✅ Archivos OK

REM Instalar dependencias si es necesario
if not exist "node_modules" (
    echo 📦 Instalando dependencias...
    npm install
    if errorlevel 1 (
        echo ❌ Error instalando dependencias
        pause
        exit /b 1
    )
)
echo ✅ Dependencias OK

echo.
echo 🚀 INICIANDO SERVIDORES...
echo.

REM Iniciar Backend
echo 🔧 Iniciando Backend (Puerto 5001)...
start /min "XUI-Backend" cmd /c "cd /d "%~dp0backend" && node simple-server.js"

REM Esperar un poco
timeout /t 3 /nobreak >nul

REM Iniciar Frontend  
echo 🎨 Iniciando Frontend (Puerto 3000)...
start /min "XUI-Frontend" cmd /c "cd /d "%~dp0" && npm start"

echo.
echo ⏳ Esperando que los servidores inicien...
timeout /t 8 /nobreak >nul

REM Esperar tiempo suficiente para que inicien
echo 🔍 Esperando que los servidores terminen de cargar...
timeout /t 10 /nobreak >nul
echo ✅ Servidores listos

echo.
echo 🎉 ¡XUI IMPORTER INICIADO CORRECTAMENTE!
echo.
echo 🌐 Abriendo navegador...
start http://localhost:3000

echo.
echo ========================================
echo    ✅ APLICACIÓN LISTA PARA USAR
echo ========================================
echo.
echo 🌐 URL: http://localhost:3000
echo 🔧 Backend: http://localhost:5001  
echo.
echo 📋 INSTRUCCIONES:
echo    1. La aplicación se abrió en tu navegador
echo    2. Si no se abrió, ve a: http://localhost:3000
echo    3. ¡Ya puedes importar tus archivos M3U!
echo.
echo 🛑 PARA DETENER:
echo    - Cierra esta ventana
echo    - O presiona cualquier tecla
echo.

pause >nul

echo.
echo 🛑 Deteniendo servidores...
taskkill /f /im node.exe >nul 2>&1
echo ✅ Servidores detenidos

echo.
echo 👋 ¡Gracias por usar XUI IMPORTER!
timeout /t 3 /nobreak >nul
