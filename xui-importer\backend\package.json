{"name": "xui-importer-backend", "version": "1.0.0", "description": "Backend API for XUI Importer - Direct database integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["xui", "iptv", "importer", "database", "series"], "author": "XUI Importer Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2"}, "devDependencies": {"nodemon": "^3.0.2"}}