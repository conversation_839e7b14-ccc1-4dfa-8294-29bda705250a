/**
 * 🎨 Estilos para CategorySelector
 */

.category-selector {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 15px 0;
}

.category-selector-header {
  margin-bottom: 20px;
}

.category-selector-header h3 {
  color: #495057;
  margin: 0 0 10px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  font-size: 0.9rem;
  margin-top: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.95rem;
  background-color: #fff;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control:disabled {
  background-color: #e9ecef;
  opacity: 1;
  cursor: not-allowed;
}

.form-text {
  display: block;
  margin-top: 5px;
  font-size: 0.85rem;
  color: #6c757d;
  font-style: italic;
}

.category-info {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

.info-card {
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-card h4 {
  color: #495057;
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
}

.info-card p {
  margin: 8px 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.info-card p strong {
  color: #495057;
  font-weight: 600;
}

.adult-content {
  color: #dc3545;
  font-weight: 600;
  background: #f8d7da;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8rem;
}

/* Responsive */
@media (max-width: 768px) {
  .category-selector {
    padding: 15px;
    margin: 10px 0;
  }
  
  .category-selector-header h3 {
    font-size: 1.1rem;
  }
  
  .form-control {
    padding: 8px 10px;
    font-size: 0.9rem;
  }
}

/* Estados de carga */
.form-control option:first-child {
  color: #6c757d;
  font-style: italic;
}

/* Animaciones */
.category-selector {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-card {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
