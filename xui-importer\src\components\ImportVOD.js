import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, ProgressBar, Badge } from 'react-bootstrap';
import { useAppContext } from '../context/AppContext';

const ImportVOD = () => {
  const { sourceConfig, streamingServers, categories } = useAppContext();
  
  // Estados principales
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileAnalysis, setFileAnalysis] = useState(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });
  
  // Estados de configuración
  const [streamsServer, setStreamsServer] = useState('');
  const [selectedCategories, setSelectedCategories] = useState([]);

  // Función para mostrar alertas
  const displayAlert = (type, message) => {
    setAlert({ show: true, type, message });
    setTimeout(() => setAlert({ show: false, type: '', message: '' }), 5000);
  };

  // Manejar selección de archivo
  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (file.type !== 'application/x-mpegurl' && !file.name.endsWith('.m3u')) {
      displayAlert('danger', '❌ Por favor selecciona un archivo M3U válido');
      return;
    }

    setSelectedFile(file);
    
    if (window.debugLog) {
      window.debugLog(`📁 File selected: ${file.name}`, 'info');
      window.debugLog(`📊 File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`, 'info');
    }

    // Analizar archivo automáticamente
    try {
      const fileContent = await file.text();
      const response = await fetch('http://localhost:5001/api/import/parse-movies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ m3uContent: fileContent })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setFileAnalysis(result.data);
        displayAlert('success', `✅ Archivo analizado: ${result.data.total} películas detectadas`);
      } else {
        throw new Error(result.error || 'Error analizando archivo');
      }
    } catch (error) {
      console.error('Error analizando archivo:', error);
      displayAlert('danger', `❌ Error analizando archivo: ${error.message}`);
    }
  };

  // Función principal de importación
  const handleImport = async () => {
    if (!selectedFile || !fileAnalysis) {
      displayAlert('warning', '⚠️ Selecciona un archivo M3U válido primero');
      return;
    }

    if (!streamsServer) {
      displayAlert('warning', '⚠️ Selecciona un servidor de streams');
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      if (window.debugLog) {
        window.debugLog(`📥 Starting import of ${selectedFile.name}`, 'info');
        window.debugLog(`📊 File size: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`, 'info');
        window.debugLog(`🎬 Content type: VOD/Movies`, 'info');
        window.debugLog(`🖥️ Target server: ${streamsServer}`, 'info');
      }

      setImportProgress(20);

      // Importar películas directamente
      const importPayload = {
        movies: fileAnalysis.movies,
        server_id: parseInt(streamsServer),
        category_id: selectedCategories.length > 0 ? parseInt(selectedCategories[0]) : null,
        tmdb_search: sourceConfig.tmdbEnrichment || true
      };

      if (window.debugLog) {
        window.debugLog('📤 Uploading file to backend...', 'info');
      }

      setImportProgress(60);

      const importResponse = await fetch('http://localhost:5001/api/import/movies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(importPayload)
      });

      if (!importResponse.ok) {
        throw new Error(`HTTP ${importResponse.status}: ${importResponse.statusText}`);
      }

      const importResult = await importResponse.json();
      setImportProgress(100);

      if (importResult.success) {
        const successMessage = `✅ Importación completada exitosamente!\n📊 Estadísticas:\n• ${importResult.imported} películas importadas\n• ${importResult.errors} errores\n• ${importResult.movies_created || 0} películas creadas`;
        
        displayAlert('success', successMessage);

        if (window.debugLog) {
          window.debugLog(`✅ Import completed successfully: ${selectedFile.name}`, 'success');
          window.debugLog(`📊 Stats: ${JSON.stringify(importResult)}`, 'info');
        }

        // Limpiar estado después de importación exitosa
        setTimeout(() => {
          setSelectedFile(null);
          setFileAnalysis(null);
          setImportProgress(0);
        }, 3000);

      } else {
        throw new Error(importResult.error || 'Error en la importación');
      }

    } catch (error) {
      console.error('Error durante la importación:', error);
      displayAlert('danger', `❌ Error durante la importación: ${error.message}`);
      
      if (window.debugLog) {
        window.debugLog(`❌ Import failed: ${error.message}`, 'error');
      }
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <Container fluid className="p-4">
      <Row>
        <Col>
          <h2 className="mb-4">🎬 Import VOD M3U</h2>
          <p className="text-muted mb-4">
            Importa archivos M3U con películas (VOD) directamente como contenido individual.
            Cada entrada se importará como una película independiente con type=2.
          </p>
        </Col>
      </Row>

      {/* Alertas */}
      {alert.show && (
        <Row className="mb-3">
          <Col>
            <Alert variant={alert.type} onClose={() => setAlert({ show: false })} dismissible>
              <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>{alert.message}</pre>
            </Alert>
          </Col>
        </Row>
      )}

      {/* Selección de archivo */}
      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">📁 Seleccionar Archivo M3U</h5>
            </Card.Header>
            <Card.Body>
              <Form.Group>
                <Form.Label>Archivo M3U con películas</Form.Label>
                <Form.Control
                  type="file"
                  accept=".m3u,.m3u8"
                  onChange={handleFileSelect}
                  disabled={isImporting}
                />
                <Form.Text className="text-muted">
                  Selecciona un archivo M3U que contenga películas/VODs
                </Form.Text>
              </Form.Group>

              {selectedFile && (
                <div className="mt-3">
                  <Badge bg="info">📄 {selectedFile.name}</Badge>
                  <Badge bg="secondary" className="ms-2">
                    📊 {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </Badge>
                  {fileAnalysis && (
                    <Badge bg="success" className="ms-2">
                      🎬 {fileAnalysis.total} películas detectadas
                    </Badge>
                  )}
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Configuración de importación */}
      {fileAnalysis && (
        <Row className="mb-4">
          <Col>
            <Card>
              <Card.Header>
                <h5 className="mb-0">⚙️ Configuración de Importación</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Servidor de Streams *</Form.Label>
                      <Form.Select
                        value={streamsServer}
                        onChange={(e) => setStreamsServer(e.target.value)}
                        disabled={isImporting}
                        required
                      >
                        <option value="">Seleccionar servidor...</option>
                        {streamingServers.map(server => (
                          <option key={server.id} value={server.id}>
                            {server.server_name} (ID: {server.id})
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Categoría (Opcional)</Form.Label>
                      <Form.Select
                        value={selectedCategories[0] || ''}
                        onChange={(e) => setSelectedCategories(e.target.value ? [e.target.value] : [])}
                        disabled={isImporting}
                      >
                        <option value="">Sin categoría específica</option>
                        {categories
                          .filter(cat => cat.category_type === 'movie')
                          .map(category => (
                            <option key={category.id} value={category.id}>
                              {category.category_name}
                            </option>
                          ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>

                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <Badge bg="info">🎬 Tipo: Películas/VOD</Badge>
                    <Badge bg="secondary" className="ms-2">📊 Type: 2</Badge>
                    <Badge bg="secondary" className="ms-2">🔢 Series No: 0</Badge>
                  </div>
                  <Button
                    variant="primary"
                    onClick={handleImport}
                    disabled={isImporting || !streamsServer}
                    size="lg"
                  >
                    {isImporting ? '⏳ Importando...' : '🚀 Importar Películas'}
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Barra de progreso */}
      {isImporting && (
        <Row className="mb-4">
          <Col>
            <Card>
              <Card.Body>
                <h6>📊 Progreso de Importación</h6>
                <ProgressBar 
                  now={importProgress} 
                  label={`${importProgress}%`}
                  animated
                  striped
                />
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}
    </Container>
  );
};

export default ImportVOD;
